# Stage 1: Build the Next.js app
FROM node:20.14.0 AS builder

# Set working directory
WORKDIR /app

# Install dependencies
COPY package.json package-lock.json ./
RUN npm install

# Copy the rest of the source code
COPY . .

# Copy environment file
COPY .env.deploy ./.env

# Build the Next.js app
RUN npm run build

# Stage 2: Production image
FROM node:20.14.0 AS runner

# Set Working Directory
WORKDIR /app

# Copy built files and necessary assets from builder
COPY --from=builder /app/.env ./.env
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/node_modules ./node_modules

# Expose the port Next.js runs on
EXPOSE 8002

# Start the Next.js server
CMD ["npm", "start"]