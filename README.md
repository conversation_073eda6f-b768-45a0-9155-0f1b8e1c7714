# Store Manager

Produk digital untuk UMKM yang berupa sistem G<PERSON><PERSON> dan <PERSON>

## SonarQube Community Build

Sonar Scanner Config

-   [guide ref](https://docs.sonarsource.com/sonarqube-community-build/try-out-sonarqube/)
-   change .env `localhost -> host.docker.internal`
-   add in package.json or run in console/terminal `sonar -Dsonar.host.url=http://localhost:9000 -Dsonar.token=sqp_ee42d837db789851992076b4d1c98d0ce283cb2d -Dsonar.projectKey=store-manager`

-   akses:
    -   user: `admin`
    -   password: `r28Cwtl8R1fisfegxHBZkqDoIEYFJyT8ISQ5WENt8pJ7J$`

## Docker Commands

-   build `docker build -t reiichimaru/store-manager:1.0.0 .`
-   push `docker push reiichimaru/store-manager:1.0.0`
-   run docker with option `--privileged` sample `docker run -d --privileged --name toko -p 632:631 -p 8002:8002 reiichimaru/store-manager:1.0.0`

### Additional Setups

-   docker exec: create password for root user `passwd`
-   edit cups config `/etc/cups/cupsd.conf`
    -   add `ServerAlias *`
    -   edit `Listen *:631`
    -   comment `Listen /run/cups/cups.sock`
    -   add `Allow all` for each location path
    -   add <Location /admin> `AuthType Basic` and `Require valid-user`
    -   restart cups `service cups restart` (and `service cups start` make sure it's running)

## Temporary Notes

-   Buat cleansing sampah hasil import dari sqlite 2024 : `db.products.deleteMany({name:{$in:[",",",,",",,,",",,,,",",,,,,",",,,,,,",",,,,,,,",",,,,,,,,",",,,,,,,,,",",,,,,,,,,,",",,,,,,,,,,,",",,,,,,,,,,,,",",,,,,,,,,,,,,",",.",",..",".","..","...","....",".....","......",".......","........",".........","..........","...........","............",".,","???","/","/...........","////////"]}});`
-   Buat dump : `mongodump --uri="*************************************************************************************************************" --out=/opt/backup/mongodump --db=store-manager-db`
-   Buat restore : `mongorestore --uri="*************************************************************************************************************" /opt/backup/mongodump/store-manager-db --drop`
