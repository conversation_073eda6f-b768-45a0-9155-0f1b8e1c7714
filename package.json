{"name": "store-service", "version": "1.0.0", "description": "b/e service for Store Manager Web App", "main": "server.ts", "private": true, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"format": "prettier --check --write \"app/**/*.{js,ts,json,md}\"", "start": "npm run format && tsx watch --env-file=.env app/server.ts"}, "dependencies": {"@fastify/cors": "^11.1.0", "@fastify/redis": "^7.0.2", "@types/node": "^24.3.0", "dayjs": "^1.11.18", "fastify": "^5.4.0", "fastify-cron": "^1.4.0", "lodash": "^4.17.21", "mongoose": "^8.18.0", "ottoman": "^2.5.2", "puppeteer": "^24.17.1", "tsx": "^4.20.5", "typescript": "^5.9.2", "unix-print": "^1.3.2"}, "devDependencies": {"@types/lodash": "^4.17.20", "@types/mongoose": "^5.11.96", "husky": "^9.1.7", "lint-staged": "^15.5.2", "prettier": "^3.6.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"app/**/*.{js,ts,json,md}": ["prettier --check --write", "git add"]}}