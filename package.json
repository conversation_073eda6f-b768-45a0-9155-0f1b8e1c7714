{"name": "store-manager", "version": "1.0.0", "description": "f/e Store Manager Web App", "private": true, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "scripts": {"lint": "next lint", "build": "next build", "start": "next start -p 8002", "scan": "npm run format && npm run lint", "format": "prettier --check --write \"{app,component,layout,lib,models,mutations,queries,server,state,types}/**/*.{js,ts,tsx,d.ts}\"", "dev": "npm install && npm run scan && next dev -p 8002"}, "dependencies": {"@dimaskiddo/angka-terbilang-nodejs": "^1.0.9", "@types/node": "^24.0.10", "@types/react": "18.2.12", "@types/react-dom": "18.2.5", "bufferutil": "^4.0.9", "canvas": "^3.1.2", "chart.js": "4.2.1", "cloudinary": "^2.6.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "idb": "^8.0.3", "ioredis": "^5.6.1", "isomorphic-dompurify": "^2.25.0", "lodash": "^4.17.21", "mongoose": "^8.17.0", "next": "13.5.9", "next-pwa": "^5.6.0", "primeflex": "^3.3.1", "primeicons": "^7.0.0", "primereact": "10.2.1", "react": "18.2.0", "react-barcode": "^1.6.1", "react-dom": "18.2.0", "react-qr-barcode-scanner": "^2.1.8", "react-qr-code": "^2.0.18", "react-to-print": "^3.1.1", "typescript": "^5.8.3", "utf-8-validate": "^6.0.5", "uuid": "^11.1.0", "valibot": "^1.1.0", "zustand": "^5.0.4"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.16", "@types/mongoose": "^5.11.97", "@types/next-pwa": "^5.6.9", "@types/react-transition-group": "^4.4.12", "eslint": "8.43.0", "eslint-config-next": "13.4.6", "prettier": "^2.8.8", "sass": "^1.63.4"}}