import mongoose from 'mongoose';
import { ReactNode } from 'react';
import { AppMenuItem, AppMenuItemProps, AppTopbarRef, LayoutConfig, LayoutContextProps, LayoutState, MenuContextProps } from './layout';

type ChildContainerProps = {
    children: ReactNode;
};

export type { AppMenuItem, AppMenuItemProps, AppTopbarRef, ChildContainerProps, LayoutConfig, LayoutContextProps, LayoutState, MenuContextProps };

export interface MongooseCache {
    conn: typeof mongoose | null;
    promise: Promise<typeof mongoose> | null;
}
