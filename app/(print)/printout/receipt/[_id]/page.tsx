'use client';

import ReceiptToPrint from '@/component/print/ReceiptToPrint';
import { getData } from '@/queries/get';
import { useEffect, useRef, useState } from 'react';

const ReceiptPrintPage = ({ params }: { params: Promise<{ _id: string }> }) => {
    const [data, setData] = useState<any>();
    const componentRef = useRef(null);

    useEffect(() => {
        const fetching = async () => {
            try {
                const { _id } = await params;

                if (_id) {
                    setData(await getData('printout/receipt', _id));
                }
            } catch (_) {
                console.error(_);
            }
        };

        fetching();
    }, [params]);

    return <ReceiptToPrint data={data} ref={componentRef} />;
};

export default ReceiptPrintPage;
