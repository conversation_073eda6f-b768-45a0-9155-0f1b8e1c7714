'use client';

import SalesToPrint from '@/component/print/SalesToPrint';
import { getData } from '@/queries/get';
import { useEffect, useRef, useState } from 'react';

const SalesPrintPage = ({ params }: { params: Promise<{ _id: string }> }) => {
    const [data, setData] = useState<any>();
    const componentRef = useRef(null);

    useEffect(() => {
        const fetching = async () => {
            try {
                const { _id } = await params;

                if (_id) {
                    setData(await getData('printout/sales', _id));
                }
            } catch (_) {
                console.error(_);
            }
        };

        fetching();
    }, [params]);

    return <SalesToPrint data={data} ref={componentRef} />;
};

export default SalesPrintPage;
