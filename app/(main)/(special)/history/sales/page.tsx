'use client';

import { SalesHistoryByInvoice } from '@/component/item/archive/invoice.sales';
import { SalesHistoryByItem } from '@/component/item/archive/item.sales';
import { useSession } from '@/lib/client.action';
import { isRestricted } from '@/lib/server.action';
import { getList } from '@/queries/get';
import { DropdownItem } from '@/types/app';
import dayjs from 'dayjs';
import 'dayjs/locale/id';
import isBetween from 'dayjs/plugin/isBetween';
import { isEmpty, last } from 'lodash';
import { FilterMatchMode } from 'primereact/api';
import { Button } from 'primereact/button';
import { DataTableFilterMeta } from 'primereact/datatable';
import { DropdownChangeEvent } from 'primereact/dropdown';
import { Menu } from 'primereact/menu';
import { useEffect, useRef, useState } from 'react';

dayjs.locale('id');
dayjs.extend(isBetween);

const processInvoiceItem = ({ date, author, customer, reference, tax, finalPrice, products, id }: any) => {
    const datetime = date ?? author.created?.time ?? null;

    if (datetime) {
        return { id, customer, author, date: datetime, cost: finalPrice, reference: reference ?? String(id).toUpperCase(), tax: tax ?? 0, count: products.length };
    }

    return null;
};

const processProductData = (result: any[]): any[] => {
    const product: any[] = [];

    result.forEach(({ id, date, author, products, reference }) => {
        const datetime = date ?? author.created?.time ?? null;

        if (datetime) {
            (products as any[]).forEach((item) => product.push({ ...item, cost: item.price ?? 0, discount: item.discount ?? 0, author, date: datetime, parent: id, reference: reference ?? String(id).toUpperCase() }));
        }
    });

    return product;
};

const processInvoiceData = (result: any[]): any[] => {
    const sales: any[] = [];

    result.forEach((item) => {
        const processedItem = processInvoiceItem(item);

        if (processedItem) {
            sales.push(processedItem);
        }
    });

    return sales;
};

const processSalesData = (result: any[]): { sales: any[]; product: any[] } => {
    const invoices = processInvoiceData(result);
    const products = processProductData(result);

    return { sales: invoices, product: products };
};

const SalesHistory = () => {
    const [modePreview, setModePreview] = useState<'produk' | 'faktur'>('produk');
    const [records, setRecords] = useState<any[]>([]);
    const [list, setList] = useState<{ product: any[]; sales: any[] }>({ sales: [], product: [] });
    const [period, setPeriod] = useState<DropdownItem | undefined>();
    const [periods, setPeriods] = useState<DropdownItem[]>([]);
    const [loading, setLoading] = useState(true);
    const [filters, setFilters] = useState<DataTableFilterMeta>({});
    const [globalFilterValue, setGlobalFilterValue] = useState('');
    const [dateFilter, setDateFilter] = useState<(Date | null)[] | null>(null);
    const modeMenu = useRef<Menu>(null);
    const { scanning } = useSession();

    const initFilters = () => {
        setGlobalFilterValue('');
        setFilters({ global: { value: null, matchMode: FilterMatchMode.CONTAINS } });
    };

    const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        let _filtered = { ...filters };
        (_filtered['global'] as any).value = value;

        setFilters(_filtered);
        setGlobalFilterValue(value);
    };

    const onPeriodChange = async ({ value }: DropdownChangeEvent) => {
        setPeriod(value);
        setLoading(true);
        const items = await getList(`history/sales/${value?.code}`);
        setRecords(items);
        setList(processSalesData(items));
        setLoading(false);
    };

    useEffect(() => {
        const csFilter = () => {
            setLoading(true);

            if (dateFilter?.length === 2) {
                const filtered = records.filter(({ date, author }) => {
                    const datetime = date ?? author.created?.time ?? null;

                    if (datetime) {
                        return dateFilter?.at(1) ? dayjs(datetime).isBetween(dayjs(dateFilter[0]), dayjs(dateFilter[1]), 'days', '[]') : dayjs(datetime).isSame(dayjs(dateFilter[0]), 'day');
                    } else {
                        return false;
                    }
                });
                setList(processSalesData(filtered));
            } else {
                setList(processSalesData(records));
            }

            setLoading(false);
        };

        csFilter();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dateFilter]);

    useEffect(() => {
        const fetching = async () => {
            const options = await getList('history');
            const listing = options.map(({ period }) => ({ code: period, name: period }));

            if (!isEmpty(listing)) {
                setPeriod(last(listing));
                const items = await getList(`history/sales/${last(listing)?.code}`);
                setRecords(items);
                setList(processSalesData(items));
            } else {
                setList({ sales: [], product: [] });
            }

            setPeriods(listing);
            setLoading(false);
            initFilters();
        };

        setLoading(true);
        fetching();
    }, []);

    useEffect(() => {
        isRestricted().then(({ disabled }) => {
            if (disabled) {
                window.location.href = '/';
            }
        });

        scanning();
    }, [scanning]);

    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <div className="flex justify-content-between align-items-center">
                        <h5>Riwayat Barang Keluar</h5>
                        <div>
                            <Button rounded text icon="pi pi-ellipsis-v" className="p-button-plain" tooltip="Menampilkan data berdasarkan ..." tooltipOptions={{ position: 'left' }} onClick={(e) => modeMenu.current?.toggle(e)} />
                            <Menu
                                popup
                                ref={modeMenu}
                                model={[
                                    { label: 'Produk', icon: 'pi pi-fw pi-box', command: () => setModePreview('produk') },
                                    { label: 'Faktur', icon: 'pi pi-fw pi-file', command: () => setModePreview('faktur') }
                                ]}
                            />
                        </div>
                    </div>
                    <p>Menampilkan data berdasarkan {modePreview === 'produk' ? 'Produk' : 'Faktur'}</p>
                    {modePreview === 'produk' ? (
                        <SalesHistoryByItem
                            list={list.product}
                            filters={filters}
                            period={period}
                            periods={periods}
                            onPeriodChange={onPeriodChange}
                            globalFilterValue={globalFilterValue}
                            onGlobalFilterChange={onGlobalFilterChange}
                            dateFilter={dateFilter}
                            setDateFilter={setDateFilter}
                            loading={loading}
                        />
                    ) : (
                        <SalesHistoryByInvoice
                            list={list.sales}
                            filters={filters}
                            period={period}
                            periods={periods}
                            onPeriodChange={onPeriodChange}
                            globalFilterValue={globalFilterValue}
                            onGlobalFilterChange={onGlobalFilterChange}
                            dateFilter={dateFilter}
                            setDateFilter={setDateFilter}
                            loading={loading}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default SalesHistory;
