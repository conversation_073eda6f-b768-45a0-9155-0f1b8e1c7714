'use client';

import { getDefaultProduct, processRangePrice, useSession } from '@/lib/client.action';
import { isRestricted } from '@/lib/server.action';
import { getList } from '@/queries/get';
import { DropdownItem } from '@/types/app';
import { isEmpty, last } from 'lodash';
import { FilterMatchMode } from 'primereact/api';
import { Column } from 'primereact/column';
import { DataTable, DataTableFilterMeta } from 'primereact/datatable';
import { Dropdown } from 'primereact/dropdown';
import { Image } from 'primereact/image';
import { InputText } from 'primereact/inputtext';
import { useEffect, useState } from 'react';

const StockHistory = () => {
    const [list, setList] = useState<any[]>([]);
    const [period, setPeriod] = useState<DropdownItem | undefined>();
    const [periods, setPeriods] = useState<DropdownItem[]>([]);
    const [loading, setLoading] = useState(true);
    const [filters, setFilters] = useState<DataTableFilterMeta>({});
    const [globalFilterValue, setGlobalFilterValue] = useState('');
    const { scanning } = useSession();

    const initFilters = () => {
        setGlobalFilterValue('');
        setFilters({ global: { value: null, matchMode: FilterMatchMode.CONTAINS } });
    };

    const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        let _filtered = { ...filters };
        (_filtered['global'] as any).value = value;

        setFilters(_filtered);
        setGlobalFilterValue(value);
    };

    const renderHeader = () => {
        return (
            <div className="flex justify-content-between flex-wrap gap-3">
                <Dropdown
                    filter
                    id="period"
                    value={period}
                    options={periods}
                    optionLabel="name"
                    placeholder="Periode Arsip"
                    onChange={async ({ value }) => {
                        setPeriod(value);
                        setLoading(true);
                        const items = await getList(`history/products/${value?.code}`);
                        setList(items);
                        setLoading(false);
                    }}
                />
                <div className="flex justify-around gap-3 flex-wrap">
                    <span className="p-input-icon-left filter-input–table">
                        <i className="pi pi-search" />
                        <InputText value={globalFilterValue} onChange={onGlobalFilterChange} placeholder="Pencarian" />
                    </span>
                </div>
            </div>
        );
    };

    const nameBodyTemplate = (rowData: any) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
            <Image alt="product image" src={rowData?.images?.at(0) ?? getDefaultProduct()} width="32" height="32" style={{ verticalAlign: 'middle' }} imageStyle={{ borderRadius: '50%', objectFit: 'cover' }} />
            <span style={{ marginLeft: '.5em', verticalAlign: 'middle' }}>{rowData.name}</span>
        </div>
    );
    const bundleBodyTemplate = (rowData: any) => (rowData?.bundle?.node && rowData?.bundle?.contain ? `${rowData.bundle.node?.amount} ${rowData.bundle.node?.unit?.name} = ${rowData.bundle.contain?.amount} ${rowData.bundle.contain?.unit?.name}` : '');
    const stockBodyTemplate = (rowData: any) => (rowData?.inventory ? `${Intl.NumberFormat('id-ID', { style: 'decimal' }).format(rowData.inventory)} ${rowData?.unit?.name ?? ''}` : '');
    const costBodyTemplate = (rowData: any) => processRangePrice(rowData?.cost ?? []);

    useEffect(() => {
        isRestricted().then(({ disabled }) => {
            if (disabled) {
                window.location.href = '/';
            }
        });

        scanning();
    }, [scanning]);

    useEffect(() => {
        const fetching = async () => {
            const options = await getList('history');
            const listing = options.map(({ period }) => ({ code: period, name: period }));

            if (!isEmpty(listing)) {
                setPeriod(last(listing));
                const items = await getList(`history/products/${last(listing)?.code}`);
                setList(items);
            }

            setPeriods(listing);
            setLoading(false);
            initFilters();
        };

        setLoading(true);
        fetching();
    }, []);

    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <h5>Riwayat Stok Produk</h5>
                    <p>Stok yang ditampilkan menggunakan total aktual dalam satuan terkecilnya</p>
                    <DataTable
                        className="p-datatable-gridlines"
                        header={renderHeader}
                        loading={loading}
                        filters={filters}
                        value={list}
                        rows={10}
                        paginatorTemplate="CurrentPageReport FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink"
                        currentPageReportTemplate="Menampilkan {first} - {last} , dari total {totalRecords} data"
                        dataKey="id"
                        filterDisplay="menu"
                        emptyMessage="Data kosong/tidak ditemukan!"
                        paginator
                        showGridlines
                        stripedRows
                        scrollable
                    >
                        <Column header="Nama" filterField="name" body={nameBodyTemplate} />
                        <Column header="Kategori" field="category.name" />
                        <Column header="Satuan" field="unit.name" />
                        <Column header="Bundel" body={bundleBodyTemplate} />
                        <Column header="Stok" filterField="inventory" body={stockBodyTemplate} />
                        <Column header="Modal" filterField="cost" body={costBodyTemplate} />
                    </DataTable>
                </div>
            </div>
        </div>
    );
};

export default StockHistory;
