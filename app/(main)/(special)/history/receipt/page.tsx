'use client';

import { ReceiptHistoryByInvoice } from '@/component/item/archive/invoice.receipt';
import { ReceiptHistoryByItem } from '@/component/item/archive/item.receipt';
import { calculateSumCost, useSession } from '@/lib/client.action';
import { isRestricted } from '@/lib/server.action';
import { getList } from '@/queries/get';
import { DropdownItem } from '@/types/app';
import dayjs from 'dayjs';
import 'dayjs/locale/id';
import isBetween from 'dayjs/plugin/isBetween';
import { isEmpty, last } from 'lodash';
import { FilterMatchMode } from 'primereact/api';
import { Button } from 'primereact/button';
import { DataTableFilterMeta } from 'primereact/datatable';
import { DropdownChangeEvent } from 'primereact/dropdown';
import { Menu } from 'primereact/menu';
import { useEffect, useRef, useState } from 'react';

dayjs.locale('id');
dayjs.extend(isBetween);

const processInvoiceItem = ({ date, author, supplier, reference, products, id }: any) => {
    const datetime = date ?? author.created?.time ?? null;

    if (datetime) {
        return { id, supplier, author, date: datetime, cost: calculateSumCost(products), reference: reference ?? String(id).toUpperCase(), count: products.length };
    }

    return null;
};

const processProductData = (result: any[]): any[] => {
    const product: any[] = [];

    result.forEach(({ id, date, author, products, reference }) => {
        const datetime = date ?? author.created?.time ?? null;

        if (datetime) {
            (products as any[]).forEach((item) => product.push({ ...item, cost: item.cost ?? 0, discount: item.discount ?? 0, author, date: datetime, parent: id, reference: reference ?? String(id).toUpperCase() }));
        }
    });

    return product;
};

const processInvoiceData = (result: any[]): any[] => {
    const receipt: any[] = [];

    result.forEach((item) => {
        const processedItem = processInvoiceItem(item);

        if (processedItem) {
            receipt.push(processedItem);
        }
    });

    return receipt;
};

const processReceiptData = (result: any[]): { receipt: any[]; product: any[] } => {
    const receipts = processInvoiceData(result);
    const products = processProductData(result);

    return { receipt: receipts, product: products };
};

const ReceiptHistory = () => {
    const [modePreview, setModePreview] = useState<'produk' | 'faktur'>('produk');
    const [records, setRecords] = useState<any[]>([]);
    const [list, setList] = useState<{ product: any[]; receipt: any[] }>({ receipt: [], product: [] });
    const [period, setPeriod] = useState<DropdownItem | undefined>();
    const [periods, setPeriods] = useState<DropdownItem[]>([]);
    const [loading, setLoading] = useState(true);
    const [filters, setFilters] = useState<DataTableFilterMeta>({});
    const [globalFilterValue, setGlobalFilterValue] = useState('');
    const [dateFilter, setDateFilter] = useState<(Date | null)[] | null>(null);
    const modeMenu = useRef<Menu>(null);
    const { scanning } = useSession();

    const initFilters = () => {
        setGlobalFilterValue('');
        setFilters({ global: { value: null, matchMode: FilterMatchMode.CONTAINS } });
    };

    const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        let _filtered = { ...filters };
        (_filtered['global'] as any).value = value;

        setFilters(_filtered);
        setGlobalFilterValue(value);
    };

    const onPeriodChange = async ({ value }: DropdownChangeEvent) => {
        setPeriod(value);
        setLoading(true);
        const items = await getList(`history/receipts/${value?.code}`);
        setRecords(items);
        setList(processReceiptData(items));
        setLoading(false);
    };

    useEffect(() => {
        const csFilter = () => {
            setLoading(true);

            if (dateFilter?.length === 2) {
                const filtered = records.filter(({ date, author }) => {
                    const datetime = date ?? author.created?.time ?? null;

                    if (datetime) {
                        return dateFilter?.at(1) ? dayjs(datetime).isBetween(dayjs(dateFilter[0]), dayjs(dateFilter[1]), 'days', '[]') : dayjs(datetime).isSame(dayjs(dateFilter[0]), 'day');
                    } else {
                        return false;
                    }
                });
                setList(processReceiptData(filtered));
            } else {
                setList(processReceiptData(records));
            }

            setLoading(false);
        };

        csFilter();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dateFilter]);

    useEffect(() => {
        const fetching = async () => {
            const options = await getList('history');
            const listing = options.map(({ period }) => ({ code: period, name: period }));

            if (!isEmpty(listing)) {
                setPeriod(last(listing));
                const items = await getList(`history/receipts/${last(listing)?.code}`);
                setRecords(items);
                setList(processReceiptData(items));
            } else {
                setList({ receipt: [], product: [] });
            }

            setPeriods(listing);
            setLoading(false);
            initFilters();
        };

        setLoading(true);
        fetching();
    }, []);

    useEffect(() => {
        isRestricted().then(({ disabled }) => {
            if (disabled) {
                window.location.href = '/';
            }
        });

        scanning();
    }, [scanning]);

    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <div className="flex justify-content-between align-items-center">
                        <h5>Riwayat Barang Masuk</h5>
                        <div>
                            <Button rounded text icon="pi pi-ellipsis-v" className="p-button-plain" tooltip="Menampilkan data berdasarkan ..." tooltipOptions={{ position: 'left' }} onClick={(e) => modeMenu.current?.toggle(e)} />
                            <Menu
                                popup
                                ref={modeMenu}
                                model={[
                                    { label: 'Produk', icon: 'pi pi-fw pi-box', command: () => setModePreview('produk') },
                                    { label: 'Faktur', icon: 'pi pi-fw pi-file', command: () => setModePreview('faktur') }
                                ]}
                            />
                        </div>
                    </div>
                    <p>Menampilkan data berdasarkan {modePreview === 'produk' ? 'Produk' : 'Faktur'}</p>
                    {modePreview === 'produk' ? (
                        <ReceiptHistoryByItem
                            list={list.product}
                            period={period}
                            periods={periods}
                            onPeriodChange={onPeriodChange}
                            filters={filters}
                            globalFilterValue={globalFilterValue}
                            onGlobalFilterChange={onGlobalFilterChange}
                            dateFilter={dateFilter}
                            setDateFilter={setDateFilter}
                            loading={loading}
                        />
                    ) : (
                        <ReceiptHistoryByInvoice
                            list={list.receipt}
                            period={period}
                            periods={periods}
                            onPeriodChange={onPeriodChange}
                            filters={filters}
                            globalFilterValue={globalFilterValue}
                            onGlobalFilterChange={onGlobalFilterChange}
                            dateFilter={dateFilter}
                            setDateFilter={setDateFilter}
                            loading={loading}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default ReceiptHistory;
