'use client';

import { calculateDataSheetMonthly, formatRp, getDefaultProduct, useSession } from '@/lib/client.action';
import { IndexedDB } from '@/lib/const';
import { DebitStatus } from '@/lib/enum';
import { getCache } from '@/lib/indexed';
import { getSignedSession, isRestricted } from '@/lib/server.action';
import { UserDocument } from '@/models/user.schema';
import { getDataNoParam } from '@/queries/get';
import { ChartData } from 'chart.js';
import dayjs from 'dayjs';
import 'dayjs/locale/id';
import isBetween from 'dayjs/plugin/isBetween';
import { reverse } from 'lodash';
import Link from 'next/link';
import { FilterMatchMode } from 'primereact/api';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Chart } from 'primereact/chart';
import { Column } from 'primereact/column';
import { DataTable, DataTableFilterMeta } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { Skeleton } from 'primereact/skeleton';
import { useEffect, useState } from 'react';

dayjs.locale('id');
dayjs.extend(isBetween);

const greeting = () => {
    const currentHour = new Date().getHours();

    if (currentHour < 11) {
        return 'pagi';
    } else if (currentHour < 13) {
        return 'siang';
    } else if (currentHour < 18) {
        return 'sore';
    } else {
        return 'malam';
    }
};

const latestBodyTemplate = () => {
    const imageBody = ({ product, parent }: any) => (
        <Link href={`/sales/${parent}`}>
            <img className="shadow-2" src={product?.images?.at(0) ?? getDefaultProduct()} alt={product?.name} width="50" />
        </Link>
    );
    const nameBody = ({ product, parent }: any) => <Link href={`/sales/${parent}`}>{product?.name ?? ' '}</Link>;
    const costBody = ({ price, discount }: any) => formatRp(price, discount);
    const openBody = ({ parent }: any) => (
        <Link href={`/sales/${parent}`}>
            <Button icon="pi pi-search" outlined text />
        </Link>
    );

    return { imageBody, nameBody, costBody, openBody };
};

const mostBodyTemplate = (segment: string) => {
    const imageBody = ({ record }: any) => (
        <Link href={`/${segment}/${record?._id}`}>
            <img className="shadow-2" src={record?.images?.at(0) ?? getDefaultProduct()} alt={record?.name} width="50" />
        </Link>
    );
    const nameBody = ({ record }: any) => (segment !== 'category' ? <Link href={`/${segment}/${record?._id}`}>{record?.name ?? ' '}</Link> : record?.name ?? ' ');
    const costBody = ({ count }: any) => formatRp(count);
    const openBody = ({ record }: any) => (
        <Link href={`/${segment}/${record?._id}`}>
            <Button icon="pi pi-search" outlined text />
        </Link>
    );

    return { nameBody, openBody, ...(segment === 'product' && { imageBody }), ...(segment === 'customer' && { costBody }) };
};

const normalize = (sales: any[], loans: any[], byDates?: (Date | null)[] | null) => {
    const filtered: any[] = [];
    const dates = byDates ?? [new Date(), null];
    let count = 0;

    reverse(sales).forEach(({ date, author, finalPrice, subPrice, reference }) => {
        const datetime = date ?? author.created?.time ?? null;

        if (datetime) {
            let loan = 0;
            const dataLoan = loans.find(({ loan, status }) => loan?.reference === reference && status !== DebitStatus.paid);

            if (dataLoan) {
                loan = dataLoan?.money ?? 0;

                ((dataLoan?.instalment as any[]) ?? []).forEach(({ money }) => {
                    loan -= money;
                });
            }

            filtered.push({ date: dayjs(datetime).format('DD MMMM YYYY'), income: formatRp(finalPrice), revenue: formatRp(subPrice - loan), nett: formatRp(subPrice), loan: formatRp(loan), datetime, cash: subPrice - loan });
        }
    });

    const results = filtered.filter(({ datetime }) => (dates?.at(1) ? dayjs(datetime).isBetween(dayjs(dates[0]), dayjs(dates[1]), 'days', '[]') : dayjs(datetime).isSame(dayjs(dates[0]), 'day')));

    results.forEach(({ cash }) => {
        count += cash;
    });

    return { list: results, count };
};

const TableHeader = ({
    sum,
    globalFilterValue,
    onGlobalFilterChange,
    dateFilter,
    setDateFilter
}: {
    sum: number;
    globalFilterValue: string;
    dateFilter: (Date | null)[] | null;
    // eslint-disable-next-line no-unused-vars
    onGlobalFilterChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    // eslint-disable-next-line no-unused-vars
    setDateFilter: (value: (Date | null)[] | null) => void;
}) => (
    <div className="flex justify-content-between flex-wrap gap-3">
        <div>
            <p className="text-3xl">{formatRp(sum)}</p>
        </div>
        <div className="flex justify-around gap-3 flex-wrap">
            <span className="p-input-icon-left filter-input–table">
                <i className="pi pi-search" />
                <InputText value={globalFilterValue} onChange={onGlobalFilterChange} placeholder="Pencarian" />
            </span>
            <Calendar showIcon showButtonBar readOnlyInput hideOnDateTimeSelect placeholder="Filter tanggal" dateFormat="dd/mm/yy" selectionMode="range" value={dateFilter} onChange={({ value }) => setDateFilter(value ?? null)} />
        </div>
    </div>
);

const Dashboard = () => {
    const [signed, setSigned] = useState<UserDocument | undefined>();
    const [restricted, setRestricted] = useState(true);
    const [loading, setLoading] = useState(true);
    const [spinning, setSpinning] = useState(true);
    const [stats, setStats] = useState<any>();
    const [filteredSales, setFilteredSales] = useState<any[]>([]);
    const [filteredCount, setFilteredCount] = useState(0);
    const [allSales, setAllSales] = useState<any[]>([]);
    const [lineData, setLineData] = useState<ChartData | undefined>();
    const [filters, setFilters] = useState<DataTableFilterMeta>({});
    const [globalFilterValue, setGlobalFilterValue] = useState('');
    const [dateFilter, setDateFilter] = useState<(Date | null)[] | null>();
    const { scanning } = useSession();

    const initFilters = () => {
        setGlobalFilterValue('');
        setFilters({ global: { value: null, matchMode: FilterMatchMode.CONTAINS } });
    };

    const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        let _filtered = { ...filters };
        (_filtered['global'] as any).value = value;

        setFilters(_filtered);
        setGlobalFilterValue(value);
    };

    const fillStat = (stats?: any) => {
        if (stats) {
            setStats(stats);
            const calculated = calculateDataSheetMonthly(stats?.records?.sales?.all ?? [], stats?.records?.loans ?? []);
            const { count, list } = normalize(stats?.records?.sales?.all ?? [], stats?.records?.loans ?? []);
            setLineData({ labels: calculated.labels, datasets: calculated.datasets });
            setAllSales(calculated.tables);
            setFilteredSales(list);
            setFilteredCount(count);
        }
    };

    const caching = async () => {
        const cached = await getCache();
        const analyzed = await cached.get(IndexedDB.STORE, IndexedDB.QUERY);

        if (analyzed) {
            fillStat(analyzed);
        }

        setLoading(false);
    };

    const fetching = async () => {
        const stats = await getDataNoParam('stat');

        if (stats) {
            const cached = await getCache();
            await cached.put(IndexedDB.STORE, stats, IndexedDB.QUERY);
            fillStat(stats);
        }

        setSpinning(false);
    };

    useEffect(() => {
        const { count, list } = normalize(stats?.records?.sales?.all ?? [], stats?.records?.loans ?? [], dateFilter);
        setFilteredSales(list);
        setFilteredCount(count);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dateFilter]);

    useEffect(() => {
        const attendance = async () => {
            const signed = await getSignedSession();
            const { visible } = await isRestricted();

            if (signed) {
                setRestricted(!visible);
                setSigned(signed);
                initFilters();
                await caching();
                await fetching();
            }
        };

        attendance();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        scanning();
    }, [scanning]);

    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <div className="surface-0">
                        <ul className="list-none p-0 m-0 flex align-items-center font-medium mb-3">
                            <li>
                                <a href="/" className="text-500 no-underline line-height-3 cursor-pointer">
                                    {spinning ? 'Sinkronisasi ...' : 'Beranda'}
                                </a>
                            </li>
                            <li className="px-2">
                                <i className="pi pi-angle-right text-500 line-height-3"></i>
                            </li>
                            <li>{spinning ? <i className="pi pi-spin pi-spinner text-900" /> : <span className="text-900 line-height-3 pi pi-check" />}</li>
                        </ul>
                        <div className="flex align-items-start flex-column lg:justify-content-between lg:flex-row">
                            <div>
                                <div className="font-medium text-3xl text-900">
                                    Selamat {greeting()}, {signed?.name ?? signed?.privilege ?? 'Operator'}
                                </div>
                                <div className="flex align-items-center text-700 flex-wrap">
                                    <div className="mr-5 flex align-items-center mt-3">
                                        <i className="pi pi-key mr-2"></i>
                                        <span>{signed?.privilege}</span>
                                    </div>
                                    <div className="mr-5 flex align-items-center mt-3">
                                        <i className="pi pi-calendar mr-2"></i>
                                        <span>{dayjs().format('dddd, DD MMMM YYYY')}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {loading ? (
                <Skeleton width="100%" height="50em" />
            ) : (
                <>
                    {!restricted && (
                        <>
                            <div className="col-12 lg:col-6 xl:col-3">
                                <div className="card mb-0">
                                    <div className="flex justify-content-between mb-3">
                                        <div>
                                            <span className="block text-500 font-medium mb-3">Produk</span>
                                            <div className="text-900 font-medium text-xl">{stats?.count?.products ?? 0}</div>
                                        </div>
                                        <div className="flex align-items-center justify-content-center bg-blue-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                                            <i className="pi pi-box text-blue-500 text-xl" />
                                        </div>
                                    </div>
                                    {stats?.count?.empties > 0 ? (
                                        <>
                                            <span className="text-orange-500 font-medium">{stats?.count?.empties ?? 0}</span>
                                            <span className="text-500"> stok habis</span>
                                        </>
                                    ) : (
                                        <span className="font-medium">&nbsp;</span>
                                    )}
                                </div>
                            </div>
                            <div className="col-12 lg:col-6 xl:col-3">
                                <div className="card mb-0">
                                    <div className="flex justify-content-between mb-3">
                                        <div>
                                            <span className="block text-500 font-medium mb-3">Pendapatan</span>
                                            <div className="text-900 font-medium text-xl">{stats?.count?.sales ?? 0}</div>
                                        </div>
                                        <div className="flex align-items-center justify-content-center bg-green-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                                            <i className="pi pi-money-bill text-green-500 text-xl" />
                                        </div>
                                    </div>
                                    <span className="text-green-500 font-medium">{formatRp(stats?.calculate?.revenue ?? 0)}</span>
                                </div>
                            </div>
                            <div className="col-12 lg:col-6 xl:col-3">
                                <div className="card mb-0">
                                    <div className="flex justify-content-between mb-3">
                                        <div>
                                            <span className="block text-500 font-medium mb-3">Hutang</span>
                                            <div className="text-900 font-medium text-xl">{stats?.count?.debts ?? 0}</div>
                                        </div>
                                        <div className="flex align-items-center justify-content-center bg-red-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                                            <i className="pi pi-credit-card text-red-500 text-xl" />
                                        </div>
                                    </div>
                                    <span className="text-red-500 font-medium">{formatRp(stats?.calculate?.debt ?? 0)}</span>
                                </div>
                            </div>
                            <div className="col-12 lg:col-6 xl:col-3">
                                <div className="card mb-0">
                                    <div className="flex justify-content-between mb-3">
                                        <div>
                                            <span className="block text-500 font-medium mb-3">Piutang</span>
                                            <div className="text-900 font-medium text-xl">{stats?.count?.loans ?? 0}</div>
                                        </div>
                                        <div className="flex align-items-center justify-content-center bg-orange-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                                            <i className="pi pi-wallet text-orange-500 text-xl" />
                                        </div>
                                    </div>
                                    <span className="text-orange-500 font-medium">{formatRp(stats?.calculate?.loan ?? 0)}</span>
                                </div>
                            </div>
                        </>
                    )}

                    <div className="col-12 lg:col-6 xl:col-3">
                        <div className="card mb-0">
                            <div className="flex justify-content-between mb-3">
                                <div>
                                    <span className="block text-500 font-medium mb-3">Pelanggan</span>
                                    <div className="text-900 font-medium text-xl">{stats?.count?.customers ?? 0}</div>
                                </div>
                                <div className="flex align-items-center justify-content-center bg-cyan-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                                    <i className="pi pi-user text-cyan-500 text-xl" />
                                </div>
                            </div>
                            <span className="font-medium">&nbsp;</span>
                        </div>
                    </div>
                    <div className="col-12 lg:col-6 xl:col-3">
                        <div className="card mb-0">
                            <div className="flex justify-content-between mb-3">
                                <div>
                                    <span className="block text-500 font-medium mb-3">Supplier</span>
                                    <div className="text-900 font-medium text-xl">{stats?.count?.suppliers ?? 0}</div>
                                </div>
                                <div className="flex align-items-center justify-content-center bg-purple-100 border-round" style={{ width: '2.5rem', height: '2.5rem' }}>
                                    <i className="pi pi-truck text-purple-500 text-xl" />
                                </div>
                            </div>
                            <span className="font-medium">&nbsp;</span>
                        </div>
                    </div>
                    <div className="col-12">
                        <hr />
                    </div>

                    {!restricted && (
                        <>
                            <div className="col-12">
                                <div className="card">
                                    <h5>Tabel Pendapatan Harian</h5>
                                    <DataTable
                                        value={filteredSales}
                                        filters={filters}
                                        rows={10}
                                        header={<TableHeader sum={filteredCount} globalFilterValue={globalFilterValue} onGlobalFilterChange={onGlobalFilterChange} dateFilter={dateFilter ?? null} setDateFilter={setDateFilter} />}
                                        paginator
                                        scrollable
                                    >
                                        <Column header="Tanggal" field="date" sortable />
                                        <Column header="Kotor" field="income" sortable />
                                        <Column header="Bersih" field="nett" sortable />
                                        <Column header="Piutang" field="loan" sortable />
                                        <Column header="Kas" field="revenue" sortable />
                                    </DataTable>
                                </div>
                            </div>
                            <div className="col-12 xl:col-6">
                                <div className="card">
                                    <h5>Grafik Pendapatan Bulanan</h5>
                                    <Chart
                                        type="line"
                                        data={lineData}
                                        style={{ marginTop: '50px', marginBottom: '50px' }}
                                        options={{
                                            plugins: { legend: { labels: { color: '#495057' } } },
                                            scales: {
                                                x: { ticks: { color: '#495057' }, grid: { color: '#ebedef' } },
                                                y: { ticks: { color: '#495057' }, grid: { color: '#ebedef' } }
                                            }
                                        }}
                                    />
                                </div>
                            </div>
                            <div className="col-12 xl:col-6">
                                <div className="card">
                                    <h5>Tabel Pendapatan Bulanan</h5>
                                    <DataTable value={allSales} rows={5} paginator scrollable>
                                        <Column header="Periode" field="period" sortable />
                                        <Column header="Kotor" field="income" sortable />
                                        <Column header="Bersih" field="nett" sortable />
                                        <Column header="Piutang" field="loan" sortable />
                                        <Column header="Kas" field="revenue" sortable />
                                    </DataTable>
                                </div>
                            </div>
                        </>
                    )}

                    {!restricted && (
                        <div className="col-12 xl:col-6">
                            <div className="card">
                                <h5>Barang Keluar Terkini</h5>
                                <DataTable value={stats?.records?.sales?.recent ?? []} rows={5} paginator scrollable>
                                    <Column header="Gambar" filterField="product.name" body={latestBodyTemplate().imageBody} style={{ width: '10%' }} />
                                    <Column header="Produk" filterField="product.name" sortable body={latestBodyTemplate().nameBody} />
                                    <Column header="Harga" filterField="price" sortable body={latestBodyTemplate().costBody} />
                                    <Column body={latestBodyTemplate().openBody} style={{ width: '10%' }} />
                                </DataTable>
                            </div>
                        </div>
                    )}

                    <div className="col-12 xl:col-6">
                        <div className="card">
                            <h5>Barang Terlaris</h5>
                            <DataTable value={stats?.records?.highest?.products ?? []} rows={5} paginator scrollable>
                                <Column header="Gambar" filterField="record.name" body={mostBodyTemplate('product').imageBody} style={{ width: '10%' }} />
                                <Column header="Produk" filterField="record.name" sortable body={mostBodyTemplate('product').nameBody} />
                                <Column header="Transaksi" field="count" sortable />
                                <Column body={mostBodyTemplate('product').openBody} style={{ width: '10%' }} />
                            </DataTable>
                        </div>
                    </div>

                    {!restricted && (
                        <div className="col-12 xl:col-6">
                            <div className="card">
                                <h5>Pelanggan Teratas</h5>
                                <DataTable value={stats?.records?.highest?.customers ?? []} rows={5} paginator scrollable>
                                    <Column header="Pelanggan" filterField="record.name" sortable body={mostBodyTemplate('customer').nameBody} />
                                    <Column header="Total" filterField="count" sortable body={mostBodyTemplate('customer').costBody} />
                                </DataTable>
                            </div>
                        </div>
                    )}

                    <div className="col-12 xl:col-6">
                        <div className="card">
                            <h5>Kategori Terlaris</h5>
                            <DataTable value={stats?.records?.highest?.categories ?? []} rows={5} paginator scrollable>
                                <Column header="Kategori" filterField="record.name" sortable body={mostBodyTemplate('category').nameBody} />
                                <Column header="Transaksi" field="count" sortable />
                            </DataTable>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
};

export default Dashboard;
