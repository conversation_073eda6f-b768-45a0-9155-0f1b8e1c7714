'use client';

import { useSession } from '@/lib/client.action';
import { useEffect } from 'react';

const UserGuide = () => {
    const { scanning } = useSession();

    useEffect(() => {
        scanning();
    }, [scanning]);

    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <h5>Empty Page</h5>
                    <p>Use this page to start from scratch and place your custom content.</p>
                </div>
            </div>
        </div>
    );
};

export default UserGuide;
