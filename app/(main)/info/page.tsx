'use client';

import InfoForm from '@/component/info/form';
import { getDefaultLogo, useSession } from '@/lib/client.action';
import { getSignedSession, isRestricted } from '@/lib/server.action';
import { UserDocument } from '@/models/user.schema';
import { submitting } from '@/mutations/submit';
import { getDataNoParam } from '@/queries/get';
import '@/styles/_form.scss';
import { useRouter } from 'next/navigation';
import { Skeleton } from 'primereact/skeleton';
import { Toast } from 'primereact/toast';
import { useEffect, useRef, useState } from 'react';
import * as validator from 'valibot';

const payloadSchema = validator.object({
    name: validator.pipe(validator.string('Nama profil usaha harus berupa huruf'), validator.nonEmpty('Nama profil usaha harus diisi'), validator.minLength(2, '<PERSON>a profil usaha minimal 2 huruf')),
    address: validator.nullish(validator.string(), ''),
    logo: validator.nullish(validator.string(), getDefault<PERSON>ogo()),
    line1: validator.nullish(validator.string(), ''),
    line2: validator.nullish(validator.string(), ''),
    customer: validator.nullish(validator.number(), 0),
    supplier: validator.nullish(validator.number(), 0)
});

const doSubmit = async (record: any) => {
    let saved = false;
    let notices: string[] = [];
    const validated = validator.safeParse(payloadSchema, record, { abortPipeEarly: true });

    if (validated.success) {
        saved = await submitting('info', record);
    } else {
        notices = validated.issues.map(({ message }) => message);
    }

    return { saved, notices };
};

const InfoPage = () => {
    const [record, setRecord] = useState();
    const [loading, setLoading] = useState(true);
    const toast = useRef<Toast | null>(null);
    const router = useRouter();
    const [signedSession, setSignedSession] = useState<UserDocument | undefined>();
    const { scanning } = useSession();

    useEffect(() => {
        if (signedSession) {
            isRestricted().then(({ disabled }) => {
                if (disabled) {
                    router.replace('/');
                }
            });
        }
    }, [router, signedSession]);

    useEffect(() => {
        const fetching = async () => {
            try {
                setRecord(await getDataNoParam('info'));
                setLoading(false);
            } catch (_) {
                console.error(_);
            }
        };

        const attendance = async () => {
            const signed = await getSignedSession();

            if (signed) {
                setSignedSession(signed);
            }
        };

        setLoading(true);
        attendance();
        fetching();
    }, []);

    useEffect(() => {
        scanning();
    }, [scanning]);

    return (
        <div className="grid">
            <div className="col-12">{loading ? <Skeleton className="w-full h-screen" /> : <InfoForm record={record} toast={toast.current} doSubmit={doSubmit} />}</div>
            <Toast ref={toast} />
        </div>
    );
};

export default InfoPage;
