'use client';

import { useSession } from '@/lib/client.action';
import { isRestricted } from '@/lib/server.action';
import { submitting } from '@/mutations/submit';
import { useRouter } from 'next/navigation';
import { Button } from 'primereact/button';
import { useEffect } from 'react';

const StockTake = () => {
    const router = useRouter();
    const { scanning } = useSession();

    useEffect(() => {
        scanning();
    }, [scanning]);

    useEffect(() => {
        isRestricted().then(({ disabled }) => {
            if (disabled) {
                router.replace('/');
            }
        });
    }, [router]);

    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <h5>Tutup Buku</h5>
                    <p>
                        Tutup buku adalah proses untuk memisahkan data berdasarkan periode pembukuan. <b>Disarankan untuk melakukan proses ini secara teratur, agar sistem tidak lambat karena memproses data transaksi yang banyak.</b>{' '}
                    </p>
                </div>
            </div>

            <div className="col-12 lg:col-5">
                <div className="p-3 h-full">
                    <div className="shadow-2 p-3 h-full flex flex-column" style={{ borderRadius: '6px' }}>
                        <div className="text-900 font-medium text-xl mb-2">Catatan</div>
                        <div className="text-600">Tahapan tutup buku adalah :</div>
                        <hr className="my-3 mx-0 border-top-1 border-bottom-none border-300" />
                        <ul className="list-none p-0 m-0 flex-grow-1">
                            <li className="flex align-items-center mb-3">
                                <i className="pi pi-check-circle text-green-500 mr-2"></i>
                                <span>Menyalin beberapa data transaksi saat ini.</span>
                            </li>
                            <li className="flex align-items-center mb-3">
                                <i className="pi pi-check-circle text-green-500 mr-2"></i>
                                <span>Meringkas data untuk tahun terbaru.</span>
                            </li>
                        </ul>
                        <div className="mt-5 text-600">Data yang diringkas adalah :</div>
                        <hr className="my-3 mx-0 border-top-1 border-bottom-none border-300" />
                        <ul className="list-none p-0 m-0 flex-grow-1">
                            <li className="flex align-items-center mb-3">
                                <i className="pi pi-check-circle text-green-500 mr-2"></i>
                                <span>Semua hutang & piutang yang lunas akan diarsipkan.</span>
                            </li>
                            <li className="flex align-items-center mb-3">
                                <i className="pi pi-check-circle text-green-500 mr-2"></i>
                                <span>Semua barang masuk (pengadaan/pembelian) akan diarsipkan.</span>
                            </li>
                            <li className="flex align-items-center mb-3">
                                <i className="pi pi-check-circle text-green-500 mr-2"></i>
                                <span>Semua barang keluar (penjualan/pendapatan) akan diarsipkan.</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div className="col-12 lg:col-7">
                <div className="p-3 h-full">
                    <div className="shadow-2 p-3 h-full flex flex-column" style={{ borderRadius: '6px' }}>
                        <div className="text-900 font-medium text-xl mb-2">Peringatan</div>
                        <div className="text-600">Data setelah tutup buku tidak dapat dirubah, tetapi masih bisa diakses.</div>
                        <hr className="my-3 mx-0 border-top-1 border-bottom-none border-300" />
                        <ul className="list-none p-0 m-0 flex-grow-1">
                            <li className="flex align-items-center mb-3">
                                <i className="pi pi-exclamation-circle text-orange-500 mr-2"></i>
                                <span>Selama proses tutup buku, sementara sistem tidak akan dapat digunakan</span>
                            </li>
                            <li className="flex align-items-center mb-3">
                                <i className="pi pi-exclamation-circle text-orange-500 mr-2"></i>
                                <span>Data yang sudah diarsipkan tidak dapat dikembalikan</span>
                            </li>
                            <li className="flex align-items-center mb-3">
                                <i className="pi pi-exclamation-circle text-orange-500 mr-2"></i>
                                <span>
                                    Hasil arsip (tutup buku) dapat dilihat di menu{' '}
                                    <a href="/stock/take" className="no-underline text-500">
                                        Tutup Buku
                                    </a>{' '}
                                    <i className="pi pi-angle-right text-500 line-height-3"></i> <span className="text-500 line-height-3">Riwayat</span>
                                </span>
                            </li>
                            <li className="flex align-items-center mb-3">
                                <i className="pi pi-exclamation-circle text-orange-500 mr-2"></i>
                                <span>Setelah proses selesai, sistem akan kembali normal</span>
                            </li>
                        </ul>
                        <hr className="mb-3 mx-0 border-top-1 border-bottom-none border-300" />
                        <Button
                            label="Proses Tutup Buku"
                            className="p-3 w-full"
                            onClick={async () => {
                                await submitting('stock/take');
                                window.location.href = '/stock-taking';
                            }}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default StockTake;
