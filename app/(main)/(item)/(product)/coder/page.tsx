'use client';

import { getDefaultProduct, useSession } from '@/lib/client.action';
import { ProductDocument } from '@/models/product.schema';
import { getList } from '@/queries/get';
import '@/styles/_primereact.scss';
import Link from 'next/link';
import { FilterMatchMode } from 'primereact/api';
import { Button } from 'primereact/button';
import { Column } from 'primereact/column';
import { DataTable, DataTableFilterMeta } from 'primereact/datatable';
import { Image } from 'primereact/image';
import { InputText } from 'primereact/inputtext';
import { OrderList } from 'primereact/orderlist';
import { useCallback, useEffect, useRef, useState } from 'react';
import Barcode from 'react-barcode';
import QRCode from 'react-qr-code';
import { useReactToPrint } from 'react-to-print';

const CodeGenerator = () => {
    const [list, setList] = useState<ProductDocument[]>([]);
    const [loading, setLoading] = useState(true);
    const [filters, setFilters] = useState<DataTableFilterMeta>({});
    const [globalFilterValue, setGlobalFilterValue] = useState('');
    const [carts, setCarts] = useState<any[]>([]);
    const [codeMode, setCodeMode] = useState<'barcode' | 'qrcode' | 'none'>('none');
    const { scanning } = useSession();
    const printRef = useRef(null);

    const initFilters = () => {
        setGlobalFilterValue('');
        setFilters({ global: { value: null, matchMode: FilterMatchMode.CONTAINS } });
    };

    const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        let _filtered = { ...filters };
        (_filtered['global'] as any).value = value;

        setFilters(_filtered);
        setGlobalFilterValue(value);
    };

    const reactToPrintContent = () => printRef.current;
    const handlePrint = useReactToPrint({});

    const processPrint = useCallback(
        (mode: 'barcode' | 'qrcode') => {
            setCodeMode(mode);

            setTimeout(() => {
                handlePrint(reactToPrintContent);
            }, 2105);
        },
        [handlePrint]
    );

    const renderHeader = () => {
        return (
            <div className="flex justify-content-end flex-wrap">
                <span className="p-input-icon-left filter-input–table">
                    <i className="pi pi-search" />
                    <InputText value={globalFilterValue} onChange={onGlobalFilterChange} placeholder="Pencarian" />
                </span>
            </div>
        );
    };

    const renderItem = (product: any) => <div>{product?.name ?? ''}</div>;

    const nameBodyTemplate = (rowData: ProductDocument) => (
        <Link href={`/product/${rowData._id}`} style={{ display: 'flex', alignItems: 'center' }}>
            <Image alt="product image" src={rowData?.images?.at(0) ?? getDefaultProduct()} width="32" height="32" style={{ verticalAlign: 'middle' }} imageStyle={{ borderRadius: '50%', objectFit: 'cover' }} />
            <span style={{ marginLeft: '.5em', verticalAlign: 'middle' }}>{rowData.name}</span>
        </Link>
    );
    const pickBodyTemplate = (rowData: ProductDocument) => (
        <Button
            outlined
            icon={`pi ${!carts.find((item) => item._id === rowData._id) ? 'pi-plus' : 'pi-times'}`}
            onClick={() => {
                if (!carts.find((item) => item._id === rowData._id)) {
                    setCarts([...carts, rowData]);
                } else {
                    setCarts(carts.filter((item) => item._id !== rowData._id));
                }
            }}
        />
    );

    useEffect(() => {
        const fetching = async () => {
            setList(await getList('product'));
            setLoading(false);
            initFilters();
        };

        setLoading(true);
        fetching();
    }, []);

    useEffect(() => {
        scanning();
    }, [scanning]);

    return (
        <div className="grid">
            <div className="col-12 lg:col-9">
                <div className="card">
                    <h5>Tabel Produk</h5>
                    <p>
                        Pemilihan produk untuk diproses pencetakan <mark>qr/barcode</mark>
                    </p>
                    <DataTable
                        className="p-datatable-gridlines"
                        header={renderHeader}
                        loading={loading}
                        filters={filters}
                        value={list}
                        rows={10}
                        paginatorTemplate="CurrentPageReport FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink"
                        currentPageReportTemplate="Menampilkan {first} - {last} , dari total {totalRecords} data"
                        dataKey="id"
                        filterDisplay="menu"
                        emptyMessage="Data kosong/tidak ditemukan!"
                        paginator
                        showGridlines
                        stripedRows
                        scrollable
                    >
                        <Column header="Nama" filterField="name" body={nameBodyTemplate} />
                        <Column header="Kategori" field="category.name" style={{ maxWidth: '6em' }} />
                        <Column header="Satuan" field="unit.name" style={{ maxWidth: '6em' }} />
                        <Column header="" body={pickBodyTemplate} className="filter-action-button" />
                    </DataTable>
                </div>
            </div>
            <div className="col-12 lg:col-3">
                <div className="card">
                    <h5>Produk Terpilih</h5>
                    <p>
                        {carts.length > 0 && <mark>{carts.length}</mark>} {carts.length > 0 ? 'barang' : 'Barang'} yang akan dicetak qr/barcode
                    </p>
                    <OrderList filter dataKey="_id" value={carts} onChange={({ value }) => setCarts(value)} filterBy="name" itemTemplate={renderItem} />
                    <div className="flex justify-content-between flex-wrap gap-field-parent mt-5">
                        <Button label="Barcode" icon="pi pi-barcode" severity="info" disabled={!carts.length} onClick={() => processPrint('barcode')} />
                        <Button label="QR Code" icon="pi pi-qrcode" className="form-action-button" disabled={!carts.length} onClick={() => processPrint('qrcode')} />
                    </div>
                </div>
            </div>
            <div style={{ display: 'none' }}>
                <div className="col-12">
                    <hr />
                </div>
                {codeMode !== 'none' && (
                    <div ref={printRef}>
                        {carts.map((cart) => (
                            <div className="col-12" key={cart._id}>
                                {codeMode === 'barcode' && <Barcode value={cart._id} margin={20} background="" />}
                                {codeMode === 'qrcode' && <QRCode value={cart._id} size={256} style={{ height: 'auto', maxWidth: '256px', width: '256px', margin: '10px' }} />}
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
};

export default CodeGenerator;
