'use client';

import dynamic from 'next/dynamic';
import { useEffect, useState } from 'react';

const BarcodeScanner = dynamic(() => import('react-qr-barcode-scanner'), { ssr: false });

const CodeScanner = () => {
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        setIsMobile(/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)); // Check if mobile device
    }, []);

    return (
        <div className="grid">
            {isMobile ? (
                <div className="col-12">
                    <div className="card">
                        <h5>QR/Barcode Scanner</h5>
                        <p>Saat ini pemindai hanya tersedia untuk perangkat desktop</p>
                    </div>
                </div>
            ) : (
                <div className="col-12">
                    <div className="card">
                        <h5>QR/Barcode Scanner</h5>
                        <p>Saat ini pemindai hanya untuk membaca kode produk</p>
                        <BarcodeScanner
                            width="100%"
                            height={600}
                            onUpdate={(_, result) => {
                                if (result) {
                                    window.location.href = `/product/${result}`;
                                }
                            }}
                        />
                    </div>
                </div>
            )}
        </div>
    );
};

export default CodeScanner;
