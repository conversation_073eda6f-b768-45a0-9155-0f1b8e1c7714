'use client';

import { formatRp, useSession } from '@/lib/client.action';
import { DEBT_SEVERITY, DEBT_STATE } from '@/lib/const';
import { DebitDocument } from '@/models/debit.schema';
import { getList, setGlobalDateFilter } from '@/queries/get';
import dayjs from 'dayjs';
import 'dayjs/locale/id';
import { useRouter } from 'next/navigation';
import { FilterMatchMode } from 'primereact/api';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Column } from 'primereact/column';
import { DataTable, DataTableFilterMeta } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { useEffect, useState } from 'react';

dayjs.locale('id');

const processData = (result: any[]): any[] =>
    result.map(({ _id, date, author, money, status, debt, loan, instalment }) => ({
        _id,
        date,
        author,
        money,
        status,
        debt,
        loan,
        instalment,
        datetime: date ?? author.created?.time,
        whois: debt?.supplier?.name ?? loan?.customer?.name,
        type: !debt ? 'Piutang' : 'Hutang',
        mark: !debt ? 'Pelanggan' : 'Supplier'
    }));

const DebtPage = () => {
    const [list, setList] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [filters, setFilters] = useState<DataTableFilterMeta>({});
    const [globalFilterValue, setGlobalFilterValue] = useState('');
    const [dateFilter, setDateFilter] = useState<(Date | null)[] | null>(null);
    const router = useRouter();
    const { scanning } = useSession();

    const initFilters = () => {
        setGlobalFilterValue('');
        setFilters({ global: { value: null, matchMode: FilterMatchMode.CONTAINS } });
    };

    const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        let _filtered = { ...filters };
        (_filtered['global'] as any).value = value;

        setFilters(_filtered);
        setGlobalFilterValue(value);
    };

    const renderHeader = () => {
        return (
            <div className="flex justify-content-between flex-wrap gap-3">
                <Button type="button" icon="pi pi-box" label="Tambah" outlined onClick={() => router.push('/debt/baru')} />
                <div className="flex justify-around gap-3 flex-wrap">
                    <span className="p-input-icon-left filter-input–table">
                        <i className="pi pi-search" />
                        <InputText value={globalFilterValue} onChange={onGlobalFilterChange} placeholder="Pencarian" />
                    </span>
                    <Calendar showIcon showButtonBar readOnlyInput hideOnDateTimeSelect placeholder="Filter tanggal" dateFormat="dd/mm/yy" selectionMode="range" value={dateFilter} onChange={({ value }) => setDateFilter(value ?? null)} />
                </div>
            </div>
        );
    };

    const editBodyTemplate = (rowData: DebitDocument) => <Button icon="pi pi-pencil" outlined onClick={() => router.push(`/debt/${rowData._id}`)} />;
    const dateBodyTemplate = ({ datetime }: any) => dayjs(datetime).format('DD MMMM YYYY');
    const debtBodyTemplate = ({ money }: DebitDocument) => formatRp(money);
    const statusBodyTemplate = ({ status }: any) => <p className={`text-${DEBT_SEVERITY[status]}-500 font-bold`}>{DEBT_STATE[status]}</p>;
    const whoIsBodyTemplate = ({ whois, mark, _id }: any) => <Button label={whois} tooltip={mark} tooltipOptions={{ position: 'mouse' }} plain text onClick={() => router.push(`/debt/${_id}`)} />;
    const typeBodyTemplate = ({ type }: any) => <p className={`text-${type === 'Piutang' ? 'red' : 'orange'}-500 font-bold`}>{type}</p>;

    const fetching = async () => {
        try {
            const result = await getList('debt');
            setList(processData(result));
        } catch (_) {
            console.error(_);
        }

        setLoading(false);
        initFilters();
    };

    useEffect(() => {
        setLoading(true);
        fetching();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        const ssFilter = async () => {
            setLoading(true);
            await setGlobalDateFilter('debt', dateFilter);
            await fetching();
        };

        ssFilter();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dateFilter]);

    useEffect(() => {
        scanning();
    }, [scanning]);

    return (
        <div className="grid">
            <div className="col-12">
                <div className="card">
                    <h5>Tabel Hutang</h5>
                    <p>Tabel ini menampilkan daftar hutang dan piutang</p>
                    <DataTable
                        className="p-datatable-gridlines"
                        header={renderHeader}
                        loading={loading}
                        filters={filters}
                        value={list}
                        rows={10}
                        paginatorTemplate="CurrentPageReport FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink"
                        currentPageReportTemplate="Menampilkan {first} - {last} , dari total {totalRecords} data"
                        dataKey="id"
                        filterDisplay="menu"
                        emptyMessage="Data kosong/tidak ditemukan!"
                        paginator
                        showGridlines
                        stripedRows
                        scrollable
                    >
                        <Column header="Pihak" filterField="whois" body={whoIsBodyTemplate} />
                        <Column header="Nominal" filterField="money" body={debtBodyTemplate} />
                        <Column header="Tanggal" filterField="datetime" body={dateBodyTemplate} />
                        <Column header="Status" filterField="status" body={statusBodyTemplate} />
                        <Column header="Jenis" filterField="type" body={typeBodyTemplate} />
                        <Column header="" body={editBodyTemplate} className="filter-action-button" />
                    </DataTable>
                </div>
            </div>
        </div>
    );
};

export default DebtPage;
