'use client';

import DebtForm from '@/component/debt/form';
import { formatRp, toaster, useSession } from '@/lib/client.action';
import { DebitStatus } from '@/lib/enum';
import { DebitDocument } from '@/models/debit.schema';
import { submitting } from '@/mutations/submit';
import { getData } from '@/queries/get';
import dayjs from 'dayjs';
import { orderBy, remove } from 'lodash';
import { FilterMatchMode } from 'primereact/api';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Column } from 'primereact/column';
import { DataTable, DataTableFilterMeta } from 'primereact/datatable';
import { InputNumber } from 'primereact/inputnumber';
import { InputText } from 'primereact/inputtext';
import { Sidebar } from 'primereact/sidebar';
import { Skeleton } from 'primereact/skeleton';
import { Toast } from 'primereact/toast';
import { useEffect, useRef, useState } from 'react';
import { v4 as uuid } from 'uuid';
import * as validator from 'valibot';

const payloadSchema = validator.object({
    money: validator.pipe(validator.number('Nominal harus diisi'), validator.minValue(1, 'Nominal minimal Rp 1')),
    status: validator.nullish(validator.string(), DebitStatus.unpaid),
    date: validator.nullish(validator.date(), null),
    operator: validator.pipe(validator.string('Nama operator/admin harus berupa huruf'), validator.nonEmpty('Nama operator/admin harus diisi')),
    debt: validator.nullish(
        validator.object({
            supplier: validator.string('Supplier/Pemasok harus diisi'),
            reference: validator.nullish(validator.string(), ' - ')
        }),
        null
    ),
    loan: validator.nullish(
        validator.object({
            customer: validator.string('Pelanggan harus diisi'),
            reference: validator.nullish(validator.string(), ' - ')
        }),
        null
    ),
    instalment: validator.nullish(
        validator.array(
            validator.object({
                money: validator.pipe(validator.number('Nominal cicilan harus diisi'), validator.minValue(1, 'Nominal minimal Rp 1')),
                date: validator.nullish(validator.date(), null)
            })
        ),
        []
    ),
    author: validator.nullish(
        validator.object({
            created: validator.object({
                by: validator.pipe(validator.string('Nama operator/admin harus berupa huruf'), validator.nonEmpty('Nama operator/admin harus diisi')),
                time: validator.date('Tanggal pembuatan harus valid')
            })
        }),
        null
    )
});

const doSubmit = async (record: any, _id?: string) => {
    let saved = false;
    let notices: string[] = [];
    const validated = validator.safeParse(payloadSchema, record, { abortPipeEarly: true });

    if (validated.success) {
        let isValid = true;

        if (summarize(record?.money, record?.instalment) === 0) {
            record.status = DebitStatus.paid;
        } else if (summarize(record?.money, record?.instalment) < 0) {
            notices = ['Nominal cicilan melebihi total hutang!'];
            isValid = false;
        }

        if (isValid) {
            saved = await submitting('debt', record, _id);
        }
    } else {
        notices = validated.issues.map(({ message }) => message);
    }

    return { saved, notices };
};

const summarize = (total: number, instalment: any[]) => {
    let paid = 0;

    instalment.forEach(({ money }) => {
        paid += money;
    });

    return total - paid;
};

const calculate = (total: number, instalment: any[]) => {
    const final = summarize(total, instalment);

    return final < 0 ? 0 : final;
};

const Contents = ({ record, toast }: { record: DebitDocument; toast: Toast | null }) => {
    const [instalment, setInstalment] = useState<any[]>([]);
    const [visible, setVisible] = useState(false);
    const [filters, setFilters] = useState<DataTableFilterMeta>({});
    const [globalFilterValue, setGlobalFilterValue] = useState('');
    const [key, setKey] = useState('');
    const [date, setDate] = useState<Date | null>(null);
    const [money, setMoney] = useState(0);
    const [total, setTotal] = useState(0);

    const initFilters = () => {
        setGlobalFilterValue('');
        setFilters({ global: { value: null, matchMode: FilterMatchMode.CONTAINS } });
    };

    const onGlobalFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        let _filtered = { ...filters };
        (_filtered['global'] as any).value = value;

        setFilters(_filtered);
        setGlobalFilterValue(value);
    };

    const renderHeader = () => {
        return (
            <div className="flex justify-content-between flex-wrap">
                <Button type="button" icon="pi pi-plus" label="Tambah" outlined disabled={!total || record?.status === DebitStatus.paid} onClick={() => viewer()} />
                <span className="p-input-icon-left filter-input–table">
                    <i className="pi pi-search" />
                    <InputText value={globalFilterValue} onChange={onGlobalFilterChange} placeholder="Pencarian" />
                </span>
            </div>
        );
    };

    const viewer = (selected: any = null) => {
        setKey(selected?.key ?? uuid());
        setDate(selected?.date ?? null);
        setMoney(selected?.money ?? 0);
        setVisible(true);
    };

    const dateBodyTemplate = ({ date }: any) => dayjs(date).format('DD MMMM YYYY');
    const costBodyTemplate = ({ money }: any) => formatRp(money);
    const editBodyTemplate = (rowData: any) => <Button icon="pi pi-pencil" outlined disabled={record?.status === DebitStatus.paid} onClick={() => viewer(rowData)} />;

    useEffect(() => {
        initFilters();
    }, []);

    useEffect(() => {
        if ((record?.instalment ?? []).length > 0) {
            setInstalment((record?.instalment ?? []).map(({ money, date }) => ({ key: uuid(), date: dayjs(date).toDate(), money })));
        }
    }, [record]);

    return (
        <>
            <div className="md:col-7 col-12">
                <DebtForm mode={record?._id ? 'edit' : 'add'} record={record} toast={toast} doSubmit={doSubmit} instalment={instalment} setTotal={setTotal} />
            </div>
            <div className="md:col-5 col-12">
                <div className="card">
                    <h5>Pembayaran</h5>
                    {total > 0 && (
                        <p>
                            Sisa cicilan : <mark>{formatRp(calculate(total, instalment))}</mark>
                        </p>
                    )}
                    <DataTable
                        className="p-datatable-gridlines"
                        header={renderHeader}
                        filters={filters}
                        value={orderBy(instalment, ['date', 'money'], ['desc', 'asc'])}
                        rows={10}
                        paginatorTemplate="CurrentPageReport FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink"
                        currentPageReportTemplate="Menampilkan {first} - {last} , dari total {totalRecords} data"
                        dataKey="id"
                        filterDisplay="menu"
                        emptyMessage="Data kosong/tidak ditemukan!"
                        paginator
                        showGridlines
                        stripedRows
                        scrollable
                    >
                        <Column header="Tanggal" filterField="date" body={dateBodyTemplate} />
                        <Column header="Cicilan" filterField="money" body={costBodyTemplate} />
                        <Column header="" body={editBodyTemplate} className="filter-action-button" />
                    </DataTable>
                </div>
                <Sidebar visible={visible} onHide={() => setVisible(false)} baseZIndex={1000} position="right">
                    <div className="grid">
                        <div className="col-12">
                            <div className="card">
                                <div className="p-fluid formgrid grid gap-field-parent">
                                    <div className="field col-12 gap-field">
                                        <label htmlFor="date">Tanggal</label>
                                        <Calendar showIcon showButtonBar readOnlyInput hideOnDateTimeSelect id="date" placeholder="Tanggal pembayaran" dateFormat="dd/mm/yy" value={date} onChange={({ value }) => setDate(value ?? null)} />
                                        <small>Kosongkan untuk pengisian otomatis hari ini</small>
                                    </div>
                                    <div className="field col-12 gap-field">
                                        <label htmlFor="money">
                                            Nominal <sup className="text-red-500">*</sup>
                                        </label>
                                        <InputNumber id="money" placeholder="Nominal cicilan" value={money} onChange={(e) => setMoney(e.value ?? 0)} min={0} maxFractionDigits={0} mode="currency" currency="IDR" />
                                        <small>Isikan nominal 0 untuk menghapus data</small>
                                    </div>
                                </div>
                                <div className="flex justify-content-between flex-wrap">
                                    <Button label="Batal" icon="pi pi-times" severity="info" onClick={() => setVisible(false)} />
                                    <Button
                                        label="Simpan"
                                        icon="pi pi-check"
                                        className="form-side-button"
                                        onClick={() => {
                                            if (money > 0) {
                                                let payments = instalment;

                                                if (instalment.find((exist: any) => exist.key === key)) {
                                                    payments = instalment.map((updated) => {
                                                        if (updated?.key === key) {
                                                            return { ...updated, date: date ?? new Date(), money };
                                                        } else {
                                                            return updated;
                                                        }
                                                    });
                                                } else {
                                                    payments.push({ key: uuid(), date: date ?? new Date(), money });
                                                }

                                                setInstalment(payments);
                                            } else {
                                                remove(instalment, { key });
                                            }

                                            setVisible(false);
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </Sidebar>
            </div>
        </>
    );
};

const DebtPanel = ({ params }: { params: Promise<{ _id: string }> }) => {
    const [record, setRecord] = useState<any>();
    const [loading, setLoading] = useState(true);
    const toast = useRef<Toast | null>(null);
    const { scanning } = useSession();

    useEffect(() => {
        const fetching = async () => {
            try {
                const { _id } = await params;

                if (_id && _id !== 'baru') {
                    setRecord(await getData('debt', _id));
                }

                setLoading(false);
            } catch (_) {
                console.error(_);
                toaster(toast.current, [{ severity: 'error', summary: 'Gagal memuat data!', detail: 'Data tidak dapat dimuat oleh Sistem' }], 'debt');
            }
        };

        setLoading(true);
        fetching();
    }, [params]);

    useEffect(() => {
        scanning();
    }, [scanning]);

    return (
        <div className="grid">
            {loading ? <Skeleton className="w-full h-screen" /> : <Contents record={record} toast={toast.current} />}
            <Toast ref={toast} />
        </div>
    );
};

export default DebtPanel;
