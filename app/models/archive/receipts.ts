import { model, Schema } from 'ottoman';

export const Receipts = model('Receipts', {
    id: Schema.Types.String,
    reference: { type: Schema.Types.String, required: false, default: '' },
    date: { type: Schema.Types.Date, required: false, default: null },
    supplier: { type: Schema.Types.Mixed, required: false, default: null },
    products: { type: [Schema.Types.Mixed], required: true, default: [] },
    author: Schema.Types.Mixed,
    period: Schema.Types.String,
    logged: Schema.Types.Date
});
