import { model, Schema } from 'ottoman';

export const Sales = model('Sales', {
    id: Schema.Types.String,
    reference: { type: Schema.Types.String, required: false, default: '' },
    subPrice: { type: Schema.Types.Number, required: false, default: 0 },
    finalPrice: { type: Schema.Types.Number, required: false, default: 0 },
    paid: { type: Schema.Types.Number, required: false, default: 0 },
    change: { type: Schema.Types.Number, required: false, default: 0 },
    tax: { type: Schema.Types.Number, required: false, default: 0 },
    date: { type: Schema.Types.Date, required: false, default: null },
    customer: { type: Schema.Types.Mixed, required: false, default: null },
    products: { type: [Schema.Types.Mixed], required: true, default: [] },
    author: Schema.Types.Mixed,
    period: Schema.Types.String,
    logged: Schema.Types.Date
});
