import { model, Schema } from 'ottoman';

export const Debts = model('Debts', {
    id: Schema.Types.String,
    money: { type: Schema.Types.Number, required: false, default: 0 },
    debt: { type: Schema.Types.Mixed, required: false, default: null },
    loan: { type: Schema.Types.Mixed, required: false, default: null },
    instalment: { type: [Schema.Types.Mixed], required: false, default: [] },
    status: { type: Schema.Types.String, required: false, default: '' },
    date: { type: Schema.Types.Date, required: false, default: null },
    author: Schema.Types.Mixed,
    period: Schema.Types.String,
    logged: Schema.Types.Date
});
