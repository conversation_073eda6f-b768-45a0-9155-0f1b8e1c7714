import { model, Schema } from 'ottoman';

export const Products = model('Products', {
    id: Schema.Types.String,
    name: { type: Schema.Types.String, required: false, default: '' },
    initialCost: { type: Schema.Types.Number, required: false, default: 0 },
    inventory: { type: Schema.Types.Number, required: false, default: 0 },
    bundle: { type: Schema.Types.Mixed, required: false, default: null },
    category: { type: Schema.Types.Mixed, required: false, default: null },
    unit: { type: Schema.Types.Mixed, required: false, default: null },
    cost: [Schema.Types.Number],
    author: Schema.Types.Mixed,
    period: Schema.Types.String,
    logged: Schema.Types.Date
});
