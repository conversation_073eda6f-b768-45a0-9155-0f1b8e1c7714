'use server';

import { STOCK_STATE } from '@/lib/const';
import handshakeDB from '@/lib/mongo';
import redis from '@/lib/redis';
import { createErrorResponse } from '@/lib/server.action';
import productSchema, { ProductDocument } from '@/models/product.schema';
import { NextRequest } from 'next/server';

export async function GET(_: NextRequest) {
    let response: Response;

    try {
        const cached = await redis.get(STOCK_STATE.CACHED);
        let items: ProductDocument[] = [];

        if (!cached) {
            await handshakeDB();
            items = await productSchema
                .find()
                .select('-__v')
                .sort({ inventory: 'desc', name: 'asc' })
                .populate({ path: 'category', select: '-__v' })
                .populate({ path: 'unit', select: '-__v' })
                .populate({ path: 'bundle.node.unit', select: '-__v' })
                .populate({ path: 'bundle.contain.unit', select: '-__v' })
                .populate({ path: 'author.created.by', select: '-__v' })
                .populate({ path: 'author.edited.by', select: '-__v' })
                .populate({ path: 'author.deleted.by', select: '-__v' })
                .lean<ProductDocument[]>();
            await redis.set(STOCK_STATE.CACHED, JSON.stringify(items), 'EX', 3 * 60);
        } else {
            items = JSON.parse(cached);
        }

        response = Response.json(items, { status: 200 });
    } catch (error) {
        response = createErrorResponse(error);
    }

    return response;
}

export async function POST(_: NextRequest) {
    let response: Response;

    try {
        fetch(`${process.env.SERVICE_SERVER}/stock`, { method: 'GET' })
            .catch((error) => console.error(error))
            .then((response) => console.info(response?.status));
        response = Response.json({ saved: true }, { status: 200 });
    } catch (error) {
        response = createErrorResponse(error);
    }

    return response;
}
