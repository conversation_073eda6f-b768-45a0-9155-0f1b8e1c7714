'use server';

import { INVENTORY_CACHED, STOCK_STATE } from '@/lib/const';
import handshakeDB from '@/lib/mongo';
import redis from '@/lib/redis';
import { createErrorResponse } from '@/lib/server.action';
import salesSchema, { SalesDocument } from '@/models/sales.schema';
import { create } from '@/mutations/item/inventory/sales/create';
import { getInventoryDateFilter } from '@/queries/get';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { NextRequest } from 'next/server';

dayjs.extend(utc);

export async function GET(_: NextRequest) {
    let response: Response;

    try {
        const cached = await redis.get(INVENTORY_CACHED.SALES);
        let filtered: any[] = [];

        if (!cached) {
            await handshakeDB();
            const dateFilter = await getInventoryDateFilter('sales');
            let dateWhere: any = { $ne: null, $gte: dayjs().subtract(3, 'month').toDate(), $lt: dayjs().add(1, 'day').toDate() };

            if (dateFilter.length === 2) {
                if (dateFilter[0]) {
                    if (!dateFilter[1]) {
                        dateWhere = { $gte: dateFilter[0], $lt: dayjs(dateFilter[0]).add(1, 'day').toDate() };
                    } else {
                        dateWhere = { $gte: dateFilter[0], $lt: dayjs(dateFilter[1]).add(1, 'day').toDate() };
                    }
                }
            }

            const items = await salesSchema
                .find({
                    $or: [
                        { date: dateWhere },
                        {
                            $and: [{ $or: [{ date: null }, { date: { $exists: false } }] }, { 'author.created.time': dateWhere }]
                        }
                    ]
                })
                .select('-__v')
                .sort({ date: 'desc', 'author.edited.time': 'desc', 'author.created.time': 'desc' })
                .populate({
                    path: 'products.product',
                    select: '-__v -author',
                    populate: [
                        { path: 'category', select: '-__v' },
                        { path: 'unit', select: '-__v' },
                        { path: 'bundle.node.unit', select: '-__v' },
                        { path: 'bundle.contain.unit', select: '-__v' }
                    ]
                })
                .populate({ path: 'customer', select: '-__v' })
                .populate({ path: 'author.created.by', select: '-__v' })
                .populate({ path: 'author.edited.by', select: '-__v' })
                .populate({ path: 'author.deleted.by', select: '-__v' })
                .lean<SalesDocument[]>();
            // Filter out products where product population failed (is null)
            filtered = items.map((item) => ({
                ...item,
                products: item.products.filter(({ product }) => product !== null)
            }));
            await redis.set(INVENTORY_CACHED.SALES, JSON.stringify(filtered), 'EX', 3 * 60);
        } else {
            filtered = JSON.parse(cached);
        }

        response = Response.json(filtered, { status: 200 });
    } catch (error) {
        response = createErrorResponse(error);
    }

    return response;
}

export async function POST(request: NextRequest) {
    let response: Response;

    try {
        const params = await request.json();
        const saved = await create(params);
        response = Response.json({ saved: !!saved?._id }, { status: 200 });
        await redis.del(INVENTORY_CACHED.SALES, STOCK_STATE.CACHED);
    } catch (error) {
        response = createErrorResponse(error);
    }

    return response;
}
