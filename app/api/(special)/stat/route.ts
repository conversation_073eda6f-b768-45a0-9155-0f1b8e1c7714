'use server';

import redis from '@/lib/redis';
import { createErrorResponse } from '@/lib/server.action';
import { processAnalytics } from '@/queries/get';
import { NextRequest } from 'next/server';

export async function GET(_: NextRequest) {
    let response: Response;
    let analytics: any;

    try {
        const cached = await redis.get('analytics');

        if (cached) {
            analytics = JSON.parse(cached);
        } else {
            analytics = await processAnalytics();
        }

        response = Response.json(analytics ?? null, { status: 200 });
    } catch (error) {
        response = createErrorResponse(error);
    }

    return response;
}
