'use server';

import { createErrorResponse } from '@/lib/server.action';
import { processAnalytics } from '@/queries/get';
import { NextRequest } from 'next/server';

export async function GET(_: NextRequest) {
    let response: Response;

    try {
        await processAnalytics();
        response = Response.json({ analyzed: true }, { status: 200 });
    } catch (error) {
        response = createErrorResponse(error);
    }

    return response;
}
