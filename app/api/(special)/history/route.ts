'use server';

import redis from '@/lib/redis';
import { createErrorResponse } from '@/lib/server.action';
import { NextRequest } from 'next/server';

export async function GET(_: NextRequest) {
    let response: Response;
    let result: any;

    try {
        const cached = await redis.get('period:archived');

        if (cached) {
            result = JSON.parse(cached);
        } else {
            const list = await fetch(`${process.env.SERVICE_SERVER}/history`, {
                method: 'GET',
                headers: { 'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate', Pragma: 'no-cache', Expires: '0' },
                cache: 'no-store'
            });
            result = await list.json();
        }

        response = Response.json(result, { status: 200 });
    } catch (error) {
        response = createErrorResponse(error);
    }

    return response;
}
