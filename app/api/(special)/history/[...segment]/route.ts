'use server';

import redis from '@/lib/redis';
import { createErrorResponse } from '@/lib/server.action';
import { NextRequest } from 'next/server';

export async function GET(_: NextRequest, { params }: { params: Promise<{ segment: string[] }> }) {
    let response: Response;
    let result: any;

    try {
        const { segment } = await params;
        const cached = await redis.get(`${segment[0]}:${segment[1]}:archived`);

        if (cached) {
            result = JSON.parse(cached);
        } else {
            const list = await fetch(`${process.env.SERVICE_SERVER}/history/${segment[0]}/${segment[1]}`, {
                method: 'GET',
                headers: { 'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate', Pragma: 'no-cache', Expires: '0' },
                cache: 'no-store'
            });
            result = await list.json();
        }

        response = Response.json(result, { status: 200 });
    } catch (error) {
        response = createErrorResponse(error);
    }

    return response;
}
