import { FastifyRedis } from '@fastify/redis';
import { last } from 'lodash';
import handshakeArchiveDB from '../couchbase';
import { Debts } from '../models/archive/debts';
import { Period } from '../models/archive/period';
import { Products } from '../models/archive/products';
import { Receipts } from '../models/archive/receipts';
import { Sales } from '../models/archive/sales';
import { Sum } from '../models/archive/sum';

const _eachCache = async (redis: FastifyRedis, model: string, period: string) => {
    try {
        console.info(`Caching archived ${model} ...`);
        const records = await getArchivedRecords(model, period);
        await redis.set(`${model}:${period}:archived`, JSON.stringify(records), 'EX', 36000); // expired in 10 hours
        console.info(`Caching archived ${model} done`);
    } catch (error) {
        console.error(error);
    }
};

const getArchivedRecords = async (model: string, period?: string) => {
    let records: any[] = [];

    try {
        const archiveDb = await handshakeArchiveDB();
        let query;

        if (archiveDb) {
            if (period) {
                switch (model) {
                    case 'analytics':
                        query = await Sum.find({ period });
                        break;
                    case 'products':
                        query = await Products.find({ period });
                        break;
                    case 'receipts':
                        query = await Receipts.find({ period });
                        break;
                    case 'sales':
                        query = await Sales.find({ period });
                        break;
                    case 'debts':
                        query = await Debts.find({ period });
                        break;
                }
            } else if (model === 'period') {
                query = await Period.find();
            }

            records = query?.rows ?? [];
        }
    } catch (error) {
        console.error(error);
    }

    return records;
};

export const cronLatestArchive = (redis: FastifyRedis) => {
    getArchivedRecords('period')
        .then(async (periods) => {
            const period = last(periods)?.period ?? null;

            if (period) {
                await redis.set('period:archived', JSON.stringify(periods));

                try {
                    await _eachCache(redis, 'products', period);
                    await _eachCache(redis, 'receipts', period);
                    await _eachCache(redis, 'sales', period);
                    await _eachCache(redis, 'analytics', period);
                } catch (error) {
                    console.error(error);
                }
            } else {
                await redis.del('period:archived');
            }
        })
        .catch((error) => {
            console.error(error);
        });
};

export default getArchivedRecords;
