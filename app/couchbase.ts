import { Ottoman } from 'ottoman';

const handshakeArchiveDB = async () => {
    let connection = null;

    try {
        if (process.env.COUCHBASE_URL && process.env.NOSQL_DATABASE) {
            connection = new Ottoman();
            await connection.connect({ connectionString: process.env.COUCHBASE_URL, bucketName: process.env.NOSQL_DATABASE, username: process.env.NOSQL_ROOT_USERNAME, password: process.env.NOSQL_ROOT_PASSWORD });
            await connection.start();
        }
    } catch (error) {
        console.error('Cannot connect to Couchbase !!!', error);
    }

    return connection;
};

export default handshakeArchiveDB;
