/** @type {import('next').NextConfig} */

// for now disable pwa
// const withPWA = require('next-pwa')({
//     dest: 'public',
//     register: true,
//     skipWaiting: true
// });

const nextConfig = {
    reactStrictMode: true,
    experimental: { serverActions: true },
    webpack: (config) => {
        config.externals = [...config.externals, { canvas: 'canvas' }];

        return config;
    }
};

// for now disable pwa
// module.exports = withPWA(nextConfig);
module.exports = nextConfig;
