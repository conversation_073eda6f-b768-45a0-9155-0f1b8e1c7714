{"compilerOptions": {"target": "es6", "module": "esnext", "lib": ["ESNext"], "strict": true, "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowJs": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "incremental": true}, "include": ["app/**/*.ts"], "exclude": ["node_modules"]}