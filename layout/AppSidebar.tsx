'use client';

import { isRestricted } from '@/lib/server.action';
import { useEffect, useState } from 'react';
import AppMenu from './AppMenu';

const AppSidebar = () => {
    const [disabled, setDisabled] = useState(false);
    const [visible, setVisible] = useState(false);

    useEffect(() => {
        const accessing = async () => {
            const restriction = await isRestricted();
            setDisabled(restriction.disabled);
            setVisible(restriction.visible);
        };

        accessing();
    }, []);

    return <AppMenu disabled={disabled} visible={visible} />;
};

export default AppSidebar;
