import { AppMenuItem } from '@/types';
import AppMenuitem from './AppMenuitem';
import { MenuProvider } from './context/menucontext';

const AppMenu = ({ disabled, visible }: { disabled: boolean; visible: boolean }) => {
    const model: AppMenuItem[] = [
        {
            label: 'Umum',
            items: [
                { label: 'Beranda', icon: 'pi pi-fw pi-home', to: '/dashboard' },
                { label: 'Profil Usaha', icon: 'pi pi-fw pi-shop', to: '/info', disabled, visible }
            ]
        },
        {
            label: 'Barang',
            items: [
                { label: 'Kategori', icon: 'pi pi-fw pi-tag', to: '/category' },
                { label: 'Satuan', icon: 'pi pi-fw pi-bookmark', to: '/unit' },
                {
                    label: 'Produk',
                    icon: 'pi pi-fw pi-box',
                    items: [
                        { label: 'Data', icon: 'pi pi-fw pi-objects-column', to: '/product' },
                        { label: 'QR/Barcode', icon: 'pi pi-fw pi-qrcode', to: '/coder', badge: 'beta' },
                        { label: 'Pemindai', icon: 'pi pi-fw pi-expand', to: '/scanner', badge: 'beta' }
                    ]
                },
                {
                    label: 'Stok',
                    icon: 'pi pi-fw pi-warehouse',
                    items: [
                        { label: 'Aktual', icon: 'pi pi-fw pi-table', to: '/stock' },
                        { label: 'Masuk', icon: 'pi pi-fw pi-download', to: '/receipt' },
                        { label: 'Keluar', icon: 'pi pi-fw pi-upload', to: '/sales' }
                    ]
                }
            ]
        },
        {
            label: 'Aplikasi',
            items: [
                { label: 'Hutang', icon: 'pi pi-fw pi-credit-card', to: '/debt' },
                {
                    label: 'Profil',
                    icon: 'pi pi-fw pi-user-edit',
                    items: [
                        { label: 'Akun', icon: 'pi pi-fw pi-users', to: '/user', disabled, visible },
                        { label: 'Supplier', icon: 'pi pi-fw pi-truck', to: '/supplier' },
                        { label: 'Pelanggan', icon: 'pi pi-fw pi-user', to: '/customer' }
                    ]
                },
                {
                    disabled,
                    visible,
                    label: 'Tutup Buku',
                    icon: 'pi pi-fw pi-briefcase',
                    items: [
                        { label: 'Proses', icon: 'pi pi-fw pi-check-circle', to: '/stock/take' },
                        {
                            label: 'Riwayat',
                            icon: 'pi pi-fw pi-history',
                            items: [
                                { label: 'Stok', icon: 'pi pi-fw pi-table', to: '/history/stock' },
                                { label: 'Barang Masuk', icon: 'pi pi-fw pi-download', to: '/history/receipt' },
                                { label: 'Barang Keluar', icon: 'pi pi-fw pi-upload', to: '/history/sales' },
                                { label: 'Statistik', icon: 'pi pi-fw pi-chart-line', to: '/history/dashboard' }
                            ]
                        }
                    ]
                },
                { label: 'Panduan', icon: 'pi pi-fw pi-question', to: '/guide' },
                { label: 'Logout', icon: 'pi pi-fw pi-sign-out', to: '/logout' }
            ]
        }
    ];

    return (
        <MenuProvider>
            <ul className="layout-menu">
                {model
                    .filter(({ disabled, visible }) => !disabled && !visible)
                    .map((item, i) => {
                        return !item?.seperator ? <AppMenuitem item={item} root={true} index={i} key={item.label} /> : <li className="menu-separator"></li>;
                    })}
            </ul>
        </MenuProvider>
    );
};

export default AppMenu;
