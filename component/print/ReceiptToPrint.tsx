'use client';

import { formatNumber } from '@/lib/client.action';
import { ComponentToPrintProps } from '@/lib/const';
import terbilang from '@dimaskiddo/angka-terbilang-nodejs';
import dayjs from 'dayjs';
import { keys, size, slice, take } from 'lodash';
import { forwardRef, useEffect, useState } from 'react';

const emptyString = ' . . . . . ';
const stripString = '----------------';

const ReceiptToPrint = forwardRef<HTMLDivElement | null, ComponentToPrintProps>((props, ref) => {
    const [record, setRecord] = useState<any>();
    const [list, setList] = useState<{ [key: number]: any[] }>({ 1: [] });
    const [number, setNumber] = useState<{ [key: string]: number }>();
    const [page, setPage] = useState(1);
    const [total, setTotal] = useState(0);

    useEffect(() => {
        if (props?.data) {
            const items = (props.data?.products as any[]) ?? [];
            const listing: { [key: number]: any[] } = { 1: take(items, 14) };
            const no: { [key: string]: number } = {};
            let calc = 0;
            let pageAt = 1;
            let start = 14;
            let end = 14 + 25;

            if (items.length > 14) {
                pageAt = Math.ceil((items.length - 14) / 25) + 1;
            }

            for (let i = 2; i <= pageAt; i++) {
                listing[i] = slice(items, start, end);
                start = end;
                end += 25;
            }

            items.forEach(({ product, discount, cost }, at) => {
                if (product?._id) {
                    no[String(product._id)] = ++at;
                    calc += !discount ? cost : cost - (discount / 100) * cost;
                }
            });

            setPage(pageAt);
            setRecord(props.data);
            setList(listing);
            setNumber(no);
            setTotal(calc);
        }
    }, [props.data]);

    return (
        <div className="p-4 font-bold" ref={ref}>
            {keys(list).map((item) => (
                <div key={`pageAt-${item}`} className="mb-8">
                    {Number(item) === 1 ? (
                        <>
                            <div className="grid border-bottom-2 border-x-none border-top-none border-dashed border-black-500 mt-2 text-base">
                                <div className="col-7 col-offset-5">
                                    <div className="grid">
                                        <div className="col text-right">{dayjs(record?.date ?? new Date()).format('DD-MMM-YYYY')}</div>
                                        <div className="col-fixed text-left" style={{ width: '10px' }}>
                                            /
                                        </div>
                                        <div className="col text-left">NO.{record?.reference ?? emptyString}</div>
                                    </div>
                                    <div className="grid">
                                        <div className="col text-right">Supplier</div>
                                        <div className="col-fixed text-left" style={{ width: '10px' }}>
                                            :
                                        </div>
                                        <div className="col text-left">{record?.supplier?.name ?? emptyString}</div>
                                    </div>
                                    <div className="grid">
                                        <div className="col text-right">Di</div>
                                        <div className="col-fixed text-left" style={{ width: '10px' }}>
                                            :
                                        </div>
                                        <div className="col text-left">{record?.supplier?.address ?? emptyString}</div>
                                    </div>
                                    <div className="grid">
                                        <div className="col text-right">Telepon</div>
                                        <div className="col-fixed" style={{ width: '10px' }}>
                                            :
                                        </div>
                                        <div className="col text-left">{record?.supplier?.phone ?? emptyString}</div>
                                    </div>
                                </div>
                            </div>
                            <div className="grid border-bottom-2 border-x-none border-top-none border-dashed border-black-500 mt-2 mb-2 text-sm">
                                <div className="col-3">
                                    <div className="grid">
                                        <div className="col text-right">TOTAL</div>
                                        <div className="col-fixed text-left" style={{ width: '20px' }}>
                                            :
                                        </div>
                                        <div className="col-6 text-right">{formatNumber(total ?? 0)}</div>
                                    </div>
                                    <div className="grid">
                                        <div className="col text-right">ITEM</div>
                                        <div className="col-fixed text-left" style={{ width: '20px' }}>
                                            :
                                        </div>
                                        <div className="col-6 text-right">{size(record?.products) ?? 0}</div>
                                    </div>
                                    <div className="grid mt-7">
                                        <div className="col">
                                            Hal {item}/{page}
                                        </div>
                                    </div>
                                </div>
                                <div className="col">
                                    <div className="grid">
                                        <div className="col-4 text-right"> Terbilang </div>
                                        <div className="col-fixed text-center" style={{ width: '10px' }}>
                                            :{' '}
                                        </div>
                                        <div className="col text-left pl-3 pr-7">{terbilang.toTerbilang(String(total)).toUpperCase()} RUPIAH</div>
                                    </div>
                                    <div className="grid mt-6 text-center">
                                        <div className="col col-offset-2">
                                            <p className="mb-5">DIBUAT OLEH</p>
                                            <p>{record?.author?.edited?.by?.name ?? record?.author?.created?.by?.name ?? emptyString}</p>
                                        </div>
                                        <div className="col">
                                            <p className="mb-5">DICEK OLEH</p>
                                            <p>{stripString}</p>
                                        </div>
                                        <div className="col">
                                            <p className="mb-5">DIANTAR OLEH</p>
                                            <p>{stripString}</p>
                                        </div>
                                        <div className="col">
                                            <p className="mb-5">DITERIMA OLEH</p>
                                            <p>{stripString}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="grid border-bottom-2 border-x-none border-top-none border-dashed border-black-500 mb-3 text-xs">
                                <div className="col-fixed text-left" style={{ width: '40px' }}>
                                    NO
                                </div>
                                <div className="col-5 text-left">NAMA BRG</div>
                                <div className="col-1 text-right">QTY</div>
                                <div className="col-2 text-right">HARGA @</div>
                                <div className="col-1 text-right">DISC</div>
                                <div className="col-2 text-right">JUMLAH</div>
                            </div>
                        </>
                    ) : (
                        <>
                            <div className="grid border-bottom-2 border-x-none border-top-none border-dashed border-black-500 mt-6 mb-2 pt-5 text-sm">
                                <div className="col-5">
                                    Hal {item}/{page}
                                </div>
                                <div className="col-7">
                                    <div className="grid">
                                        <div className="col text-right">{dayjs(record?.date ?? new Date()).format('DD-MMM-YYYY')}</div>
                                        <div className="col-fixed text-left" style={{ width: '10px' }}>
                                            /
                                        </div>
                                        <div className="col text-left">NO.{record?.reference ?? emptyString}</div>
                                    </div>
                                </div>
                            </div>
                            <div className="grid border-bottom-2 border-x-none border-top-none border-dashed border-black-500 mb-3 text-xs">
                                <div className="col-fixed text-left" style={{ width: '40px' }}>
                                    NO
                                </div>
                                <div className="col-5 text-left">NAMA BRG</div>
                                <div className="col-1 text-right">QTY</div>
                                <div className="col-2 text-right">HARGA @</div>
                                <div className="col-1 text-right">DISC</div>
                                <div className="col-2 text-right">JUMLAH</div>
                            </div>
                        </>
                    )}
                    {list[Number(item)].map(({ product, qty, unit, cost, discount }, at) => (
                        <div className="grid text-xs" key={`item-${product?._id ?? at}`}>
                            <div className="col-fixed text-left" style={{ width: '40px' }}>
                                {number?.[String(product?._id)] ?? ++at}
                            </div>
                            <div className="col-5 text-left">{product?.name ?? emptyString}</div>
                            <div className="col-1 text-right">
                                {qty ?? 0}
                                {unit?.short ?? emptyString}
                            </div>
                            <div className="col-2 text-right">{cost > 0 && qty > 0 ? formatNumber(cost / qty) : ''}</div>
                            <div className="col-1 text-right">{!discount ? '' : `${formatNumber(discount ?? 0)}%`}</div>
                            <div className="col-2 text-right">{formatNumber(!discount ? cost : cost - (discount / 100) * cost)}</div>
                        </div>
                    ))}
                </div>
            ))}
        </div>
    );
});

ReceiptToPrint.displayName = 'ReceiptToPrint';

export default ReceiptToPrint;
