import { formatRp, getDefaultProduct, pickUnitDetail } from '@/lib/client.action';
import { DropdownItem } from '@/types/app';
import { TableInventoryProps } from '@/types/layout';
import dayjs from 'dayjs';
import 'dayjs/locale/id';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { DropdownChangeEvent } from 'primereact/dropdown';
import { Image } from 'primereact/image';
import { HistoryHeader } from '../history.header';

dayjs.locale('id');

export const ReceiptHistoryByItem = ({
    list,
    period,
    periods,
    loading,
    filters,
    globalFilterValue,
    onGlobalFilterChange,
    onPeriodChange,
    dateFilter,
    setDateFilter
}: TableInventoryProps & { period?: DropdownItem; periods: DropdownItem[]; onPeriodChange: (e: DropdownChangeEvent) => void }) => {
    const nameBodyTemplate = ({ product }: any) => (
        <>
            <Image alt="product image" src={product?.images?.at(0) ?? getDefaultProduct()} width="32" height="32" style={{ verticalAlign: 'middle' }} imageStyle={{ borderRadius: '50%', objectFit: 'cover' }} />
            <span style={{ marginLeft: '.5em', verticalAlign: 'middle' }}>{product.name}</span>
        </>
    );
    const qtyBodyTemplate = ({ qty, unit, product }: any) => `${Intl.NumberFormat('id-ID', { style: 'decimal' }).format(qty)} ${pickUnitDetail(product, unit)?.name ?? ''}`;
    const discountBodyTemplate = ({ discount }: any) => `${discount ? discount + ' %' : ''}`;
    const costBodyTemplate = ({ cost }: any) => formatRp(cost);
    const dateBodyTemplate = ({ date }: any) => dayjs(date).format('DD MMMM YYYY');

    return (
        <DataTable
            className="p-datatable-gridlines"
            header={<HistoryHeader period={period} periods={periods} onPeriodChange={onPeriodChange} globalFilterValue={globalFilterValue} onGlobalFilterChange={onGlobalFilterChange} dateFilter={dateFilter} setDateFilter={setDateFilter} />}
            loading={loading}
            filters={filters}
            value={list}
            rows={10}
            stateStorage="local"
            stateKey="receipt-item-table"
            paginatorTemplate="CurrentPageReport FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink"
            currentPageReportTemplate="Menampilkan {first} - {last} , dari total {totalRecords} data"
            dataKey="id"
            filterDisplay="menu"
            emptyMessage="Data kosong/tidak ditemukan!"
            paginator
            showGridlines
            stripedRows
            scrollable
        >
            <Column header="Tanggal" filterField="date" body={dateBodyTemplate} />
            <Column header="Produk" filterField="product.name" body={nameBodyTemplate} />
            <Column header="Kategori" field="product.category.name" />
            <Column header="Qty" filterField="qty" body={qtyBodyTemplate} />
            <Column header="Diskon" filterField="discount" body={discountBodyTemplate} />
            <Column header="Biaya" filterField="cost" body={costBodyTemplate} />
            <Column header="No Faktur" field="reference" />
        </DataTable>
    );
};
