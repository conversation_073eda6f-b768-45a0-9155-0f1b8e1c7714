import { formatRp } from '@/lib/client.action';
import { DropdownItem } from '@/types/app';
import { TableInventoryProps } from '@/types/layout';
import dayjs from 'dayjs';
import 'dayjs/locale/id';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { DropdownChangeEvent } from 'primereact/dropdown';
import { HistoryHeader } from '../history.header';

dayjs.locale('id');

export const ReceiptHistoryByInvoice = ({
    list,
    period,
    periods,
    loading,
    filters,
    globalFilterValue,
    onGlobalFilterChange,
    onPeriodChange,
    dateFilter,
    setDateFilter
}: TableInventoryProps & { period?: DropdownItem; periods: DropdownItem[]; onPeriodChange: (e: DropdownChangeEvent) => void }) => {
    const dateBodyTemplate = ({ date }: any) => dayjs(date).format('DD MMMM YYYY');
    const costBodyTemplate = (rowData: any) => formatRp(rowData.cost);

    return (
        <DataTable
            className="p-datatable-gridlines"
            header={<HistoryHeader period={period} periods={periods} onPeriodChange={onPeriodChange} globalFilterValue={globalFilterValue} onGlobalFilterChange={onGlobalFilterChange} dateFilter={dateFilter} setDateFilter={setDateFilter} />}
            loading={loading}
            filters={filters}
            value={list}
            rows={10}
            stateStorage="local"
            stateKey="receipt-invoice-table"
            paginatorTemplate="CurrentPageReport FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink"
            currentPageReportTemplate="Menampilkan {first} - {last} , dari total {totalRecords} data"
            dataKey="id"
            filterDisplay="menu"
            emptyMessage="Data kosong/tidak ditemukan!"
            paginator
            showGridlines
            stripedRows
            scrollable
        >
            <Column header="Tanggal" filterField="date" body={dateBodyTemplate} />
            <Column header="No Faktur" field="reference" />
            <Column header="Supplier" field="supplier.name" />
            <Column header="Total Biaya" filterField="cost" body={costBodyTemplate} />
            <Column header="Total Barang" field="count" />
        </DataTable>
    );
};
