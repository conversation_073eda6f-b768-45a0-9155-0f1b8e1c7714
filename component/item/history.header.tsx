import { DropdownItem } from '@/types/app';
import { Calendar } from 'primereact/calendar';
import { Dropdown, DropdownChangeEvent } from 'primereact/dropdown';
import { InputText } from 'primereact/inputtext';

export const HistoryHeader = ({
    period,
    periods,
    globalFilterValue,
    dateFilter,
    onGlobalFilterChange,
    setDateFilter,
    onPeriodChange
}: {
    period?: DropdownItem;
    periods: DropdownItem[];
    globalFilterValue: string;
    onGlobalFilterChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    dateFilter: (Date | null)[] | null;
    setDateFilter: (value: (Date | null)[] | null) => void;
    onPeriodChange: (e: DropdownChangeEvent) => void;
}) => (
    <div className="flex justify-content-between flex-wrap gap-3">
        <Dropdown filter id="period" value={period} options={periods} optionLabel="name" placeholder="Periode Arsip" onChange={onPeriodChange} />
        <div className="flex justify-around gap-3 flex-wrap">
            <span className="p-input-icon-left filter-input–table">
                <i className="pi pi-search" />
                <InputText value={globalFilterValue} onChange={onGlobalFilterChange} placeholder="Pencarian" />
            </span>
            <Calendar showIcon showButtonBar readOnlyInput hideOnDateTimeSelect placeholder="Filter tanggal" dateFormat="dd/mm/yy" selectionMode="range" value={dateFilter} onChange={({ value }) => setDateFilter(value ?? null)} />
        </div>
    </div>
);
