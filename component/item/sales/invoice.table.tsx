import SalesToPrint from '@/component/print/SalesToPrint';
import { formatRp } from '@/lib/client.action';
import { SalesDocument } from '@/models/sales.schema';
import { getData } from '@/queries/get';
import { TableInventoryProps } from '@/types/layout';
import dayjs from 'dayjs';
import 'dayjs/locale/id';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { SplitButton } from 'primereact/splitbutton';
import { Toast } from 'primereact/toast';
import { useCallback, useRef, useState } from 'react';
import { useReactToPrint } from 'react-to-print';
import { InventoryHeader } from '../inventory.header';

dayjs.locale('id');

export const TableBySales = ({ list, loading, filters, globalFilterValue, onGlobalFilterChange, dateFilter, setDateFilter }: TableInventoryProps) => {
    const router = useRouter();
    const [printData, setPrintData] = useState<any>();
    const toast = useRef<Toast | null>(null);
    const printRef = useRef(null);

    const reactToPrintContent = () => printRef.current;
    const handlePrint = useReactToPrint({});

    const processPrint = useCallback(
        async (id: string) => {
            const data = await getData('printout/sales', id);
            setPrintData(data);

            setTimeout(() => {
                handlePrint(reactToPrintContent);
                toast.current?.show({ severity: 'success', summary: 'Berhasil mencetak', detail: 'Data berhasil dicetak' });
            }, 2105);
        },
        [handlePrint]
    );

    const referenceBodyTemplate = (rowData: SalesDocument) => (
        <Link href={`/sales/${rowData._id}`} style={{ display: 'flex', alignItems: 'center' }}>
            {rowData.reference}
        </Link>
    );
    const editBodyTemplate = (rowData: SalesDocument) => (
        <SplitButton
            outlined
            icon="pi pi-pencil"
            onClick={() => router.push(`/sales/${rowData._id}`)}
            model={[
                // {
                //     label: 'Cetak (Service)',
                //     icon: 'pi pi-print',
                //     command: () => {
                //         toast.current?.show({ severity: 'info', summary: 'Memproses cetak', detail: `Melakukan permintaan untuk mencetak Faktur No.${rowData?.reference}` });
                //         printing('sales', String(rowData._id)).then((printed) => {
                //             const status: string = printed ? 'Berhasil' : 'Gagal';
                //             toast.current?.show({ severity: 'success', summary: `${status} mencetak`, detail: `Data ${status.toLowerCase()} dicetak` });
                //         });
                //     }
                // }, DEPRECATED !!!
                {
                    label: 'Cetak',
                    icon: 'pi pi-file-pdf',
                    command: () => {
                        toast.current?.show({ severity: 'info', summary: 'Memproses cetak', detail: `Melakukan permintaan untuk mencetak Faktur No.${rowData?.reference}` });
                        processPrint(String(rowData._id));
                    }
                }
            ]}
        />
    );
    const dateBodyTemplate = ({ date }: any) => dayjs(date).format('DD MMMM YYYY');
    const costBodyTemplate = (rowData: any) => formatRp(rowData.cost);
    const taxBodyTemplate = ({ tax }: any) => `${tax ? tax + ' %' : ''}`;

    return (
        <>
            <DataTable
                className="p-datatable-gridlines"
                header={<InventoryHeader addUrl="/sales/baru" globalFilterValue={globalFilterValue} onGlobalFilterChange={onGlobalFilterChange} dateFilter={dateFilter} setDateFilter={setDateFilter} />}
                loading={loading}
                filters={filters}
                value={list}
                rows={10}
                stateStorage="local"
                stateKey="sales-invoice-table"
                paginatorTemplate="CurrentPageReport FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink"
                currentPageReportTemplate="Menampilkan {first} - {last} , dari total {totalRecords} data"
                dataKey="id"
                filterDisplay="menu"
                emptyMessage="Data kosong/tidak ditemukan!"
                paginator
                showGridlines
                stripedRows
                scrollable
            >
                <Column header="Tanggal" filterField="date" body={dateBodyTemplate} />
                <Column header="No Faktur" filterField="reference" body={referenceBodyTemplate} />
                <Column header="Pelanggan" field="customer.name" />
                <Column header="PPN" filterField="tax" body={taxBodyTemplate} />
                <Column header="Total Biaya" filterField="cost" body={costBodyTemplate} />
                <Column header="" body={editBodyTemplate} className="filter-action-button" />
            </DataTable>
            <Toast ref={toast} />
            <div style={{ display: 'none' }}>
                <SalesToPrint data={printData} ref={printRef} />;
            </div>
        </>
    );
};
