import { handleFailedSave, toaster } from '@/lib/client.action';
import { DebitStatus } from '@/lib/enum';
import { getSignedSession } from '@/lib/server.action';
import { DebitDocument } from '@/models/debit.schema';
import { UserDocument } from '@/models/user.schema';
import { getList } from '@/queries/get';
import { DropdownItem, SubmitResponse } from '@/types/app';
import dayjs from 'dayjs';
import 'dayjs/locale/id';
import { useRouter } from 'next/navigation';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Column } from 'primereact/column';
import { DataTable } from 'primereact/datatable';
import { Dropdown } from 'primereact/dropdown';
import { InputNumber } from 'primereact/inputnumber';
import { InputText } from 'primereact/inputtext';
import { Sidebar } from 'primereact/sidebar';
import { Toast } from 'primereact/toast';
import { SetStateAction, useEffect, useState } from 'react';

dayjs.locale('id');

const createLogEntry = (activity: any, action: string): any => ({ name: activity?.by?.name, time: dayjs(activity?.time).format('DD MMM YYYY HH:mm:ss'), action });

const createLogs = (author: any): any[] => {
    const createdLog = createLogEntry(author.created, 'Dibuat');
    const editedLog = author.edited ? createLogEntry(author.edited, 'Diubah') : null;

    return [createdLog, ...(editedLog ? [editedLog] : [])];
};

const fetchSuppliers = async () => {
    const suppliers: DropdownItem[] = [];

    try {
        const list = await getList('supplier');
        suppliers.push(...list.map(({ _id, name }: any) => ({ name, code: _id })));
    } catch (_) {
        console.error(_);
    }

    return suppliers;
};

const fetchCustomers = async () => {
    const customers: DropdownItem[] = [];

    try {
        const list = await getList('customer');
        customers.push(...list.map(({ _id, name }: any) => ({ name, code: _id })));
    } catch (_) {
        console.error(_);
    }

    return customers;
};

const categories: DropdownItem[] = [
    { code: 'Hutang', name: 'Hutang' },
    { code: 'Piutang', name: 'Piutang' }
];

const DebtForm = ({
    mode,
    record,
    toast,
    doSubmit,
    setTotal,
    instalment
}: {
    mode: 'add' | 'edit';
    record: DebitDocument | undefined | null;
    toast: Toast | null;
    doSubmit: (record: any, _id?: string) => Promise<SubmitResponse>;
    setTotal: (value: SetStateAction<number>) => void;
    instalment?: any[];
}) => {
    const [customers, setCustomers] = useState<DropdownItem[]>([]);
    const [suppliers, setSuppliers] = useState<DropdownItem[]>([]);
    const [supplier, setSupplier] = useState<DropdownItem | undefined>();
    const [customer, setCustomer] = useState<DropdownItem | undefined>();
    const [category, setCategory] = useState<DropdownItem>(categories[0]);
    const [signedSession, setSignedSession] = useState<UserDocument | undefined>();
    const [author, setAuthor] = useState<any>();
    const [locking, setLocking] = useState(false);
    const [title, setTitle] = useState('Hutang');
    const [date, setDate] = useState<Date | null>(null);
    const [reference, setReference] = useState('');
    const [money, setMoney] = useState(0);
    const [logs, setLogs] = useState<any[]>([]);
    const [visible, setVisible] = useState(false);
    const router = useRouter();

    const setLogInfo = (author?: any) => setLogs(createLogs(author));

    const generatePayload = () => ({
        date,
        money,
        status: (instalment ?? []).length > 0 ? DebitStatus.instalment : DebitStatus.unpaid,
        instalment: (instalment ?? []).map(({ date, money }) => ({ date, money })),
        debt: title === 'Hutang' ? { supplier: supplier?.code ?? null, reference: reference || ' - ' } : null,
        loan: title === 'Piutang' ? { customer: customer?.code ?? null, reference: reference || ' - ' } : null,
        operator: signedSession?.username,
        ...(mode === 'edit' && { _id: record?._id, author: { ...author, created: { time: dayjs(author?.created?.time).toDate(), by: author.created?.by?._id } } })
    });

    const handleSubmitResponse = (submitted: SubmitResponse) => {
        if (submitted.saved) {
            toaster(toast, [{ severity: 'success', summary: 'Berhasil simpan', detail: 'Data berhasil disimpan di Sistem' }], 'debt');
        } else {
            setLocking(false);
            handleFailedSave(toast, submitted.notices);
        }
    };

    const submitAction = async () => {
        if (!locking) {
            setLocking(true);
            toast?.show({ severity: 'info', summary: 'Menyimpan', detail: `Memproses penyimpanan data ${title.toLowerCase()} ...` });
            const response = await doSubmit(generatePayload(), record?._id?.toString() ?? '');
            handleSubmitResponse(response);
        }
    };

    useEffect(() => {
        if (record) {
            setTitle(record?.debt ? 'Hutang' : 'Piutang');
            setCategory(categories[record?.debt ? 0 : 1]);
            setReference(record?.debt?.reference ?? record?.loan?.reference ?? '');
            setSupplier(suppliers.find(({ code }) => code === record?.debt?.supplier?._id));
            setCustomer(customers.find(({ code }) => code === record?.loan?.customer?._id));
            setDate(record?.date ? dayjs(record?.date).toDate() : null);
            setMoney(record?.money ?? 0);
            setTotal(record?.money ?? 0);
            setAuthor(record?.author ?? null);
            setLogInfo(record?.author);
        }
    }, [customers, record, setTotal, suppliers]);

    useEffect(() => {
        const fetchOptions = async () => {
            try {
                setCustomers(await fetchCustomers());
                setSuppliers(await fetchSuppliers());
            } catch (_) {
                console.error(_);
            }
        };

        const attendance = async () => {
            const signed = await getSignedSession();

            if (signed) {
                setSignedSession(signed);
            }
        };

        fetchOptions();
        attendance();
    }, []);

    return (
        <div className="card">
            <div className="flex align-items-center justify-content-between">
                <h5>
                    {mode === 'add' ? 'Buat' : 'Ubah'} {title} {mode === 'add' ? 'Baru' : ''}
                </h5>
                <Button aria-label="Riwayat Pendataan" icon="pi pi-history" rounded text severity="help" onClick={() => setVisible(true)} tooltip="Riwayat Pendataan" tooltipOptions={{ position: 'left' }} />
            </div>
            <div className="p-fluid formgrid grid gap-field-parent">
                <div className="field md:col-5 col-12 gap-field">
                    <label htmlFor="recType">Jenis</label>
                    <Dropdown
                        id="recType"
                        value={category ?? categories[0]}
                        options={categories}
                        optionLabel="name"
                        placeholder="Jenis data"
                        onChange={({ value }) => {
                            setCategory(value);
                            setTitle(value?.code);
                        }}
                    />
                </div>
                <div className="field col-12 md:col-7">
                    <label htmlFor="reference">No Faktur</label>
                    <InputText id="reference" type="text" value={reference} onChange={({ target }) => setReference(target.value)} placeholder={`Nomor faktur ${title.toLowerCase() === 'hutang' ? 'pembelian' : 'penjualan'} barang`} />
                </div>
                <div className="field md:col-5 col-12 gap-field">
                    <label htmlFor="date">Tanggal</label>
                    <Calendar showIcon showButtonBar readOnlyInput hideOnDateTimeSelect id="date" placeholder={`Tanggal ${title.toLowerCase()}`} dateFormat="dd/mm/yy" value={date} onChange={({ value }) => setDate(value ?? null)} />
                    {mode === 'add' && <small>Kosongkan untuk pengisian otomatis hari ini</small>}
                </div>
                {category?.code === 'Hutang' && (
                    <div className="field col-12 md:col-7">
                        <label htmlFor="supplier">
                            Supplier <sup className="text-red-500">*</sup>
                        </label>
                        <Dropdown filter id="supplier" value={supplier} options={suppliers} optionLabel="name" placeholder="Supplier/Pemasok" onChange={({ value }) => setSupplier(value)} />
                    </div>
                )}
                {category?.code === 'Piutang' && (
                    <div className="field col-12 md:col-7">
                        <label htmlFor="customer">
                            Pelanggan <sup className="text-red-500">*</sup>
                        </label>
                        <Dropdown filter id="customer" value={customer} options={customers} optionLabel="name" placeholder="Pelanggan" onChange={({ value }) => setCustomer(value)} />
                    </div>
                )}
                <div className="field col-12">
                    <label htmlFor="money">
                        Nominal <sup className="text-red-500">*</sup>
                    </label>
                    <InputNumber
                        id="money"
                        placeholder={`Nominal ${title.toLowerCase()}`}
                        value={money}
                        onChange={(e) => {
                            setMoney(e.value ?? 0);
                            setTotal(e.value ?? 0);
                        }}
                        min={0}
                        maxFractionDigits={0}
                        mode="currency"
                        currency="IDR"
                    />
                    <small>Dalam rupiah (IDR)</small>
                </div>
            </div>
            <div className="flex justify-content-between flex-wrap gap-field-parent">
                <Button label="Batal" icon="pi pi-times" severity="info" onClick={() => router.replace('/debt')} />
                <Button label="Simpan" icon="pi pi-check" className="form-action-button" disabled={locking || record?.status === DebitStatus.paid} onClick={async () => await submitAction()} />
            </div>
            <Sidebar visible={visible} position="right" className="w-full md:w-25rem" onHide={() => setVisible(false)}>
                <h2>Riwayat Pendataan</h2>
                <DataTable value={logs} size="small" showGridlines stripedRows>
                    <Column field="action" header="Status" />
                    <Column field="name" header="Operator" />
                    <Column field="time" header="Waktu" />
                </DataTable>
            </Sidebar>
        </div>
    );
};

export default DebtForm;
