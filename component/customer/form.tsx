import { doCancelAction, handleFailedSave, toaster } from '@/lib/client.action';
import { CustomerDocument } from '@/models/customer.schema';
import { SubmitResponse } from '@/types/app';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Toast } from 'primereact/toast';
import { useEffect, useState } from 'react';

const CustomerForm = ({ toast, mode, record, doSubmit }: { toast: Toast | null; mode: 'add' | 'edit'; record: CustomerDocument | undefined | null; doSubmit: (record: any, _id?: string) => Promise<SubmitResponse> }) => {
    const [name, setName] = useState('');
    const [phone, setPhone] = useState('');
    const [address, setAddress] = useState('');
    const [city, setCity] = useState('');
    const [loading, setLoading] = useState(false);

    const handleSubmitResponse = (submitted: SubmitResponse) => {
        if (submitted.saved) {
            toaster(toast, [{ severity: 'success', summary: 'Berhasil simpan', detail: 'Data berhasil disimpan di Sistem' }], 'customer');
        } else {
            setLoading(false);
            handleFailedSave(toast, submitted.notices);
        }
    };

    const doAction = async () => {
        const response = await doSubmit({ name, phone, address, city, ...(mode === 'edit' && { _id: record?._id }) }, record?._id?.toString() ?? '');
        handleSubmitResponse(response);
    };

    const submitAction = async () => {
        if (!loading) {
            toast?.show({ severity: 'info', summary: 'Menyimpan', detail: 'Memproses penyimpanan data pelanggan ...' });
            setLoading(true);
            await doAction();
        }
    };

    useEffect(() => {
        if (record) {
            setName(record.name ?? '');
            setAddress(record?.address ?? '');
            setPhone(record?.phone ?? '');
            setCity(record?.city ?? '');
        }
    }, [record]);

    return (
        <div className="card">
            <h5>
                {mode === 'add' ? 'Buat' : 'Ubah'} Pelanggan {mode === 'add' ? 'Baru' : record?.name}
            </h5>
            <div className="p-fluid formgrid grid gap-field-parent">
                <div className="field col-12 md:col-6 gap-field">
                    <label htmlFor="name">
                        Nama <sup className="text-red-500">*</sup>
                    </label>
                    <InputText id="name" type="text" value={name} onChange={({ target }) => setName(target.value)} />
                </div>
                <div className="field col-12 md:col-6 gap-field">
                    <label htmlFor="phone">Telepon/HP</label>
                    <InputText id="phone" type="text" value={phone} onChange={({ target }) => setPhone(target.value)} />
                </div>
                <div className="field col-12 gap-field">
                    <label htmlFor="address">Alamat</label>
                    <InputTextarea id="address" rows={4} value={address} onChange={({ target }) => setAddress(target.value)} autoResize />
                </div>
                <div className="field col-12 md:col-6">
                    <label htmlFor="city">Kota</label>
                    <InputText id="city" type="text" value={city} onChange={({ target }) => setCity(target.value)} />
                </div>
            </div>
            <div className="flex justify-content-between flex-wrap">
                <Button label="Batal" icon="pi pi-times" severity="info" onClick={() => doCancelAction('customer')} />
                <Button label="Simpan" icon="pi pi-check" className="form-action-button" disabled={loading} onClick={async () => await submitAction()} />
            </div>
        </div>
    );
};

export default CustomerForm;
