import { DebitStatus } from '@/lib/enum';
import handshakeDB from '@/lib/mongo';
import debitSchema, { DebitDocument } from '@/models/debit.schema';
import { buildAuthorPayload } from '@/mutations/global/function';

const buildConditionalData = ({ status, debt, loan, instalment, date }: DebitDocument): Partial<DebitDocument> => ({
    status: status ?? DebitStatus.unpaid,
    date: date ?? new Date(),
    debt: debt ?? null,
    loan: loan ?? null,
    instalment: instalment ?? []
});

const buildCreateData = async (params: any): Promise<Partial<DebitDocument>> => {
    const authorized = await buildAuthorPayload(params.operator, 'create');

    return { money: params.money, ...buildConditionalData(params), ...authorized };
};

export const create = async (params: any): Promise<DebitDocument | null> => {
    let saved: DebitDocument | null = null;

    try {
        if (params?.money > 0) {
            await handshakeDB();
            const payload = await buildCreateData(params);
            saved = await debitSchema.create(payload);
        }
    } catch (_) {
        console.error(_);
    }

    return saved;
};
