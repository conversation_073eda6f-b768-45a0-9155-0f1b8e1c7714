import handshakeDB from '@/lib/mongo';
import debitSchema, { DebitDocument } from '@/models/debit.schema';
import { buildAuthorPayload } from '../global/function';

const buildConditionalData = ({ status, debt, loan, instalment, date }: DebitDocument): Partial<DebitDocument> => ({
    debt: debt ?? null,
    loan: loan ?? null,
    instalment: instalment ?? [],
    date: date ?? new Date(),
    ...(status && { status })
});

const buildUpdateData = async (params: any): Promise<Partial<DebitDocument>> => {
    const authorized = await buildAuthorPayload(params.operator, 'update', params?.author);

    return { money: params.money, ...buildConditionalData(params), ...authorized };
};

export const update = async (params: any): Promise<DebitDocument | null> => {
    let saved: DebitDocument | null = null;

    try {
        if (params?.money > 0) {
            await handshakeDB();
            const payload = await buildUpdateData(params);
            saved = await debitSchema.findOneAndUpdate({ _id: params._id }, payload, { new: true, lean: true }).lean<DebitDocument>();
        }
    } catch (_) {
        console.error(_);
    }

    return saved;
};
