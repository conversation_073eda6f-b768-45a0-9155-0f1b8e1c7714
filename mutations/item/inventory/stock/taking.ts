import { DebitStatus } from '@/lib/enum';
import handshakeDB from '@/lib/mongo';
import debitSchema from '@/models/debit.schema';
import inventorySchema from '@/models/inventory.schema';
import receiptSchema from '@/models/receipt.schema';
import salesSchema from '@/models/sales.schema';
import { processAnalytics } from '@/queries/get';
import { syncStock } from './sync';

export const cleansing = async (stocks: any[]) => {
    try {
        await handshakeDB();
        console.info('Cleansing database ...');
        await inventorySchema.deleteMany({});
        console.info('Removal receipt & sales records ...');
        await receiptSchema.deleteMany({});
        await salesSchema.deleteMany({});
        console.info('Removal paid debt/loan records ...');
        await debitSchema.deleteMany({ status: DebitStatus.paid });
        console.info('Recalculating inventory & cost ...');
        await inventorySchema.insertMany(stocks);
        await syncStock(); // sync stock
        await processAnalytics(); // run analytics
    } catch (error) {
        console.error(error);
    }
};
